{#
    SPS Eligibility LCDV Edit Template

    This template provides a form for editing existing LCDV-based eligibility rules
    for the SPS (Smart Phone Services) system. LCDV codes are vehicle identification
    codes (up to 20 characters) used to determine service eligibility.

    <AUTHOR>
    @since 2025-06-27
#}
{% extends '_layout/layout.html.twig' %}

{% block title %}{{ 'Edit LCDV Eligibility Rule' | trans }}{% endblock %}

{% block content %}
<div class="container-fluid">
    {# Page Header #}
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit me-2"></i>{{ 'Edit LCDV Eligibility Rule' | trans }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ path('sps_eligibility_index') }}">{{ 'SPS Eligibility' | trans }}</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ path('sps_eligibility_index', {'tab': 'lcdv'}) }}">{{ 'LCDV Rules' | trans }}</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">{{ 'Edit Rule' | trans }}</li>
            </ol>
        </nav>
    </div>

    {# Flash Messages #}
    {% for type, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{{ type == 'success' ? 'check-circle' : (type == 'error' ? 'exclamation-triangle' : 'info-circle') }} me-2"></i>
                {{ message | trans }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endfor %}

    {# Main Form Card #}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-code me-2"></i>{{ 'LCDV Eligibility Rule Details' | trans }}
            </h6>
        </div>
        <div class="card-body">
            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
            
            <div class="row">
                <div class="col-md-12">
                    {# LCDV Codes Field #}
                    <div class="form-group mb-4">
                        {{ form_label(form.lcdvCodesString, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                        {{ form_widget(form.lcdvCodesString, {'attr': {'class': 'form-control form-control-lg'}}) }}
                        {% if form.lcdvCodesString.vars.help %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>{{ form.lcdvCodesString.vars.help }}
                            </div>
                        {% endif %}
                        {{ form_errors(form.lcdvCodesString) }}
                    </div>

                    {# Eligibility Rules Field #}
                    <div class="form-group mb-4">
                        {{ form_label(form.eligibilityRules, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                        {{ form_widget(form.eligibilityRules, {'attr': {'class': 'form-control'}}) }}
                        {% if form.eligibilityRules.vars.help %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>{{ form.eligibilityRules.vars.help }}
                            </div>
                        {% endif %}
                        {{ form_errors(form.eligibilityRules) }}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            {# Type Field #}
                            <div class="form-group mb-4">
                                {{ form_label(form.type, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.type, {'attr': {'class': 'form-control'}}) }}
                                {% if form.type.vars.help %}
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>{{ form.type.vars.help }}
                                    </div>
                                {% endif %}
                                {{ form_errors(form.type) }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            {# Disclaimer Field #}
                            <div class="form-group mb-4">
                                <div class="form-check form-switch">
                                    {{ form_widget(form.eligibilityDisclaimer, {'attr': {'class': 'form-check-input'}}) }}
                                    {{ form_label(form.eligibilityDisclaimer, null, {'label_attr': {'class': 'form-check-label fw-bold'}}) }}
                                </div>
                                {% if form.eligibilityDisclaimer.vars.help %}
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>{{ form.eligibilityDisclaimer.vars.help }}
                                    </div>
                                {% endif %}
                                {{ form_errors(form.eligibilityDisclaimer) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {# Form Actions #}
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between">
                        <a href="{{ path('sps_eligibility_index', {'tab': 'lcdv'}) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>{{ 'Cancel' | trans }}
                        </a>
                        <div>
                            {% if profile.isSuperAdmin() or profile.isCentralAdministrator() %}
                                <a href="{{ path('sps_eligibility_lcdv_delete', {'id': sps_eligibility.id}) }}" 
                                   class="btn btn-danger me-2"
                                   onclick="return confirm('{{ 'Are you sure you want to delete this LCDV rule?' | trans }}')">
                                    <i class="fas fa-trash me-2"></i>{{ 'Delete' | trans }}
                                </a>
                            {% endif %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ 'Update LCDV Rule' | trans }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {{ form_end(form) }}
        </div>
    </div>

    {# Rule Information Card #}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-info-circle me-2"></i>{{ 'Rule Information' | trans }}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">{{ 'Current LCDV Codes' | trans }}</h6>
                    <div class="mb-3">
                        {% for code in sps_eligibility.lcdvCodes %}
                            <span class="badge bg-secondary me-1 mb-1">{{ code }}</span>
                        {% endfor %}
                    </div>
                    
                    <h6 class="text-primary">{{ 'Rule Details' | trans }}</h6>
                    <ul class="list-unstyled">
                        <li><strong>{{ 'Type' | trans }}:</strong> 
                            <span class="badge bg-info">{{ sps_eligibility.type }}</span>
                        </li>
                        <li><strong>{{ 'Disclaimer Required' | trans }}:</strong> 
                            {% if sps_eligibility.eligibilityDisclaimer %}
                                <span class="badge bg-warning">{{ 'Yes' | trans }}</span>
                            {% else %}
                                <span class="badge bg-success">{{ 'No' | trans }}</span>
                            {% endif %}
                        </li>
                        {% if sps_eligibility.createdAt %}
                            <li><strong>{{ 'Created' | trans }}:</strong> {{ sps_eligibility.createdAt|date('Y-m-d H:i:s') }}</li>
                        {% endif %}
                        {% if sps_eligibility.updatedAt %}
                            <li><strong>{{ 'Last Updated' | trans }}:</strong> {{ sps_eligibility.updatedAt|date('Y-m-d H:i:s') }}</li>
                        {% endif %}
                    </ul>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
{% endblock %}
