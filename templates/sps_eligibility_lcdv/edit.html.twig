{% extends '_layout/layout.html.twig' %}

{% block stylesheets %}
    <style>
        .form-help {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .required-field::after {
            content: " *";
            color: #dc3545;
        }
    </style>
{% endblock %}

{% block content %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'Edit SPS Eligibility LCDV Rule' | trans }}</h6>
                <div>
                    <a href="{{ path('sps_eligibility_lcdv_show', {'id': sps_eligibility.id}) }}" class="btn btn-info me-2">
                        <i class="fas fa-eye"></i> {{ 'View' | trans }}
                    </a>
                    <a href="{{ path('sps_eligibility_lcdv_index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ 'Back to List' | trans }}
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.lcdvCodesString, null, {'label_attr': {'class': 'form-label required-field'}}) }}
                            {{ form_widget(form.lcdvCodesString, {'attr': {'class': 'form-control'}}) }}
                            {{ form_help(form.lcdvCodesString) }}
                            {{ form_errors(form.lcdvCodesString) }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.type, null, {'label_attr': {'class': 'form-label required-field'}}) }}
                            {{ form_widget(form.type, {'attr': {'class': 'form-control'}}) }}
                            {{ form_help(form.type) }}
                            {{ form_errors(form.type) }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            {{ form_label(form.eligibilityRules, null, {'label_attr': {'class': 'form-label required-field'}}) }}
                            {{ form_widget(form.eligibilityRules, {'attr': {'class': 'form-control', 'rows': 5}}) }}
                            {{ form_help(form.eligibilityRules) }}
                            {{ form_errors(form.eligibilityRules) }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form_widget(form.eligibilityDisclaimer, {'attr': {'class': 'form-check-input'}}) }}
                                {{ form_label(form.eligibilityDisclaimer, null, {'label_attr': {'class': 'form-check-label'}}) }}
                            </div>
                            {{ form_help(form.eligibilityDisclaimer) }}
                            {{ form_errors(form.eligibilityDisclaimer) }}
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    <a href="{{ path('sps_eligibility_lcdv_index') }}" class="btn btn-secondary me-2">
                        {{ 'Cancel' | trans }}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {{ 'Update' | trans }}
                    </button>
                </div>

            {{ form_end(form) }}
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
{% endblock %}
