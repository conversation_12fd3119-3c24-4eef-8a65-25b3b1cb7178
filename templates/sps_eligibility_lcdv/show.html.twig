{#
    SPS Eligibility LCDV Show Template

    This template displays detailed information about a specific LCDV-based eligibility rule
    for the SPS (Smart Phone Services) system. LCDV codes are vehicle identification
    codes (up to 20 characters) used to determine service eligibility.

    <AUTHOR>
    @since 2025-06-27
#}
{% extends '_layout/layout.html.twig' %}

{% block title %}{{ 'LCDV Eligibility Rule Details' | trans }}{% endblock %}

{% block content %}
<div class="container-fluid">
    {# Page Header #}
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-eye me-2"></i>{{ 'LCDV Eligibility Rule Details' | trans }}
        </h1>
        <div class="btn-group" role="group">
            <a href="{{ path('sps_eligibility_index', {'tab': 'lcdv'}) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ 'Back to List' | trans }}
            </a>
            {% if profile.isSuperAdmin() or profile.isCentralAdministrator() %}
                <a href="{{ path('sps_eligibility_lcdv_edit', {'id': sps_eligibility.id}) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>{{ 'Edit' | trans }}
                </a>
            {% endif %}
        </div>
    </div>

    {# Rule Details Card #}
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>{{ 'Rule Information' | trans }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {# LCDV Codes #}
                        <div class="col-md-12 mb-4">
                            <h6 class="text-primary fw-bold">{{ 'LCDV Codes' | trans }}</h6>
                            <div class="mt-2">
                                {% for code in sps_eligibility.codes %}
                                    <span class="badge bg-secondary me-2 mb-2">{{ code }}</span>
                                {% endfor %}
                            </div>
                        </div>

                        {# Eligibility Rule #}
                        <div class="col-md-12 mb-4">
                            <h6 class="text-primary fw-bold">{{ 'Eligibility Rule' | trans }}</h6>
                            <div class="mt-2">
                                <div class="alert alert-info">
                                    {{ sps_eligibility.eligibilityRule|nl2br }}
                                </div>
                            </div>
                        </div>

                        {# Type and Disclaimer Row #}
                        <div class="col-md-6 mb-3">
                            <h6 class="text-primary fw-bold">{{ 'Type' | trans }}</h6>
                            <span class="badge bg-info fs-6">{{ sps_eligibility.type }}</span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-primary fw-bold">{{ 'Eligibility Disclaimer' | trans }}</h6>
                            {% if sps_eligibility.eligibilityDisclaimer %}
                                <div class="alert alert-warning">
                                    {{ sps_eligibility.eligibilityDisclaimer }}
                                </div>
                            {% else %}
                                <span class="text-muted">{{ 'No disclaimer specified' | trans }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {# Metadata Card #}
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i>{{ 'Metadata' | trans }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Rule ID' | trans }}</h6>
                        <code>{{ sps_eligibility.id }}</code>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Created At' | trans }}</h6>
                        {% if sps_eligibility.createdAt %}
                            <span class="text-muted">
                                {{ sps_eligibility.createdAt|date('Y-m-d H:i:s') }}
                            </span>
                        {% else %}
                            <span class="text-muted">{{ 'Not available' | trans }}</span>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Updated At' | trans }}</h6>
                        {% if sps_eligibility.updatedAt %}
                            <span class="text-muted">
                                {{ sps_eligibility.updatedAt|date('Y-m-d H:i:s') }}
                            </span>
                        {% else %}
                            <span class="text-muted">{{ 'Not available' | trans }}</span>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Scope' | trans }}</h6>
                        <span class="badge bg-success">{{ sps_eligibility.scope }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
