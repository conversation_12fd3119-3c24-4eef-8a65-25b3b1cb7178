<form class="d-inline-block" method="post" action="{{ path('ds_lcdv_delete', { 'id': ds_lcdv.id, 'profile': profile.id }) }}" id="form-{{ ds_lcdv.id }}" onsubmit="return false;">
	<input type="hidden" name="_method" value="DELETE">
	<input type="hidden" name="_token" value="{{ csrf_token('delete' ~ ds_lcdv.id) }}">

	<button type="button" class="btn btn-danger text-end" data-bs-toggle="modal" data-bs-target="#deleteModal{{ ds_lcdv.id }}">
		{{ 'btn_delete' | trans }}
	</button>
</form>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal{{ ds_lcdv.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ ds_lcdv.id }}" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="deleteModalLabel{{ ds_lcdv.id }}">
					{{ 'ds_lcdv_delete_title' | trans }} : {{ ds_lcdv.lcdv }}
				</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>

			<div class="modal-body">
				{{ 'ds_lcdv_delete_confirm' | trans }}
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
					{{ 'btn_cancel' | trans }}
				</button>
				<button type="button" class="btn btn-danger" onclick="document.getElementById('form-{{ ds_lcdv.id }}').setAttribute('onsubmit', 'return true'); document.getElementById('form-{{ ds_lcdv.id }}').submit();">
					{{ 'btn_delete' | trans }}
				</button>
			</div>
		</div>
	</div>
</div>
