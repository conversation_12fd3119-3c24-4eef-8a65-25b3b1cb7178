{% extends '_layout/layout.html.twig' %}


{% block stylesheets %}
	<link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
	<link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
	<style>
		.thumbnails li img {
			width: 180px;
			height: 180px;
		}
	</style>
{% endblock %}

{% block content %}
	{% for message in app.flashes('success') %}
		<div class="alert alert-success">
			{{ message | trans }}
		</div>
	{% endfor %}
	{% for message in app.flashes('error') %}
		<div class="alert alert-danger">
			{{ message | trans }}
		</div>
	{% endfor %}

	<div class="card shadow mb-4">
		<div class="overflow-auto" style="overflow-y:scroll; height: 600px;">
			<div class="card-body">
				<div class="tab-content">
					<h1 class="h3 mb-4 text-gray-800">{{ 'ev_routings' | trans | upper }}</h1>

					<div class="mb-4 text-end">
						<a role="button" class="btn btn-primary" href="{{ path('ev_routing_add') }}">
							{{ 'ev_routing_new' | trans }}
						</a>
					</div>

					<div class="table-responsive">
						<table class="table table-bordered dataTable" id="dataTable" width="100%">
							<thead>
								<tr>
									<th class="text">{{ 'ev_routing_label' | trans }}</th>
									<th class="text">{{ 'ev_routing_lcdv' | trans }}</th>
									{% if brand == 'OP' or brand == 'VX' %}
										<th class="text">{{ 'ev_routing_rpo' | trans }}</th>
									{% endif %}
									<th class="text">DVQ</th>
									<th class="text">B0F</th>
									<th class="text">DAR</th>
									<th class="text">{{ 'created_at' | trans }}</th>
									<th class="text">{{ 'updated_at' | trans }}</th>
									<th class="text text-end" width="20%">{{ 'actions' | trans }}</th>
								</tr>
							</thead>
							<tbody>
								{% for ev_routing in ev_routings %}
									<tr>
										<td>{{ ev_routing.label }}</td>
										<td>
											<p>
												{% for key, lcdvLabel in ev_routing.lcdvs %}
													{% if key == 10 %}
														<span>…</span>
													{% endif %}
													<span class="{% if key > 9 %}truncate{% endif %}">
														{{ lcdvLabel.lcdv }}<br/>
													</span>
												{% endfor %}
											</p>
										</td>
										{% if brand == 'OP' or brand == 'VX' %}
											<td>
												<p>
													{% for key, rpoLabel in ev_routing.rpos %}
														{% if key == 10 %}
															<span>…</span>
														{% endif %}
														<span class="{% if key > 9 %}truncate{% endif %}">
															{{ rpoLabel.rpo }}<br/>
														</span>
													{% endfor %}
												</p>
											</td>
										{% endif %}
										<td>{{ ev_routing.dvq }}</td>
										<td>{{ ev_routing.b0f }}</td>
										<td>{{ ev_routing.dar }}</td>
										<td>{{ ev_routing.createdAt ? ev_routing.createdAt|date('Y-m-d H:i') : '-' }}</td>
										<td>{{ ev_routing.updatedAt ? ev_routing.updatedAt|date('Y-m-d H:i') : '-' }}</td>
										<td class="text-end">
											<a class="btn btn-warning me-1" href="{{ path('ev_routing_edit', { 'profile': profile.id, 'evRouting': ev_routing.id, 'brand': brand }) }}">
												{{ 'ev_routing_update' | trans }}
											</a>
											<a href="#" class="btn btn-danger me-1"
												data-bs-toggle="modal"
	   											data-bs-target="#delete-ev-routing-modal-{{ ev_routing.id }}">
												{{ 'btn_delete' | trans }}
											</a>
										</td>
									</tr>
									<div class="modal fade" id="delete-ev-routing-modal-{{ ev_routing.id }}" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
										<form action="{{ path('ev_routing_delete', {'profile': profile.id, 'evRouting': ev_routing.id, 'brand': brand}) }}" id="delete-ev-routing-form-{{ ev_routing.id }}" method="POST">
											<input type="hidden" name="token" value="{{ csrf_token('delete-ev-routing') }}">

											<div class="modal-dialog">
												<div class="modal-content">
													<div class="modal-header">
														<h5 class="modal-title" id="delete">{{ 'confirmation' | trans }}</h5>
														<button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
															<span aria-hidden="true">×</span>
														</button>
													</div>
													<div class="modal-body">{{ 'ev_routing_delete' | trans }}</div>
													<div class="modal-footer">
														<button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ 'btn_cancel' | trans }}</button>
														<button type="submit" class="btn btn-danger">{{ 'btn_delete' | trans }}</button>
													</div>
												</div>
											</div>
										</form>
									</div>
								{% else %}
									<tr>
										<td colspan="9">{{ 'datatable.no_records' | trans }}</td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}

{% block modals %}

{% endblock %}

{% block javascripts %}
	{{ parent() }}
	<script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
	<script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
	<script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
	<script src="{{ asset('js/app.js') }}"></script>

	<script>
		$(document).ready(function () {
const table = $('#dataTable').DataTable();
table.on('draw', function () {
$(".truncate").hide();
});
$(".truncate").hide();
});

	</script>
{% endblock %}
