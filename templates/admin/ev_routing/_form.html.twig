{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ 'enabled' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.enabled) }}
                        </span>
                        {{ form_widget(form.enabled) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ 'ev_routing_label' | trans | upper }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.label) }}
                        </span>
                        {{ form_widget(form.label) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ 'brand' | trans | upper }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.brand) }}
                        </span>
                        {{ form_widget(form.brand) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ 'ev_routing_lcdv' | trans | upper }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.lcdv) }}
                        </span>
                        {{ form_widget(form.lcdv) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        DVQ <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.dvq) }}
                        </span>
                        {{ form_widget(form.dvq) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        B0F <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.b0f) }}
                        </span>
                        {{ form_widget(form.b0f) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        DAR <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.dar) }}
                        </span>
                        {{ form_widget(form.dar) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    {% if form.rpo is defined %}
                    <div class="col-md-3">
                        {{ 'RPO' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                        <span class="text-danger">
                            {{ form_errors(form.rpo) }}
                        </span>
                        {{ form_widget(form.rpo) }}
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_engine_type' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.engineType) }}
                            </span>
                                {{ form_widget(form.engineType) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_csciph' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.constantSpeedConsumptionInkWhPerHundredkm) }}
                            </span>
                                {{ form_widget(form.constantSpeedConsumptionInkWhPerHundredkm) }}
                    </div>
                </div>
            </div>

          <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_max_charging' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.maxChargeInkWh) }}
                            </span>
                                {{ form_widget(form.maxChargeInkWh) }}
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_max_speed' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.vehicleMaxSpeed) }}
                            </span>
                                {{ form_widget(form.vehicleMaxSpeed) }}
                    </div>
                </div>
            </div>

          <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_weight' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.vehicleWeight) }}
                            </span>
                                {{ form_widget(form.vehicleWeight) }}
                    </div>
                </div>
            </div>

           <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_axle_weight' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.vehicleAxleWeight) }}
                            </span>
                                {{ form_widget(form.vehicleAxleWeight) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_length' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.vehicleLength) }}
                            </span>
                                {{ form_widget(form.vehicleLength) }}
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_width' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.vehicleWidth) }}
                            </span>
                                {{ form_widget(form.vehicleWidth) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_height' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.vehicleHeight) }}
                            </span>
                                {{ form_widget(form.vehicleHeight) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ 'ev_routing_acceleration_efficiency' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.accelerationEfficiency) }}
                            </span>
                                {{ form_widget(form.accelerationEfficiency) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_deceleration_efficiency' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.decelerationEfficiency) }}
                            </span>
                                {{ form_widget(form.decelerationEfficiency) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_uphill_efficiency' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.uphillEfficiency) }}
                            </span>
                                {{ form_widget(form.uphillEfficiency) }}
                    </div>
                </div>
            </div>

         <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_downhill_efficiency' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.downhillEfficiency) }}
                            </span>
                                {{ form_widget(form.downhillEfficiency) }}
                    </div>
                </div>
            </div>

           <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                            {{ 'ev_routing_charging_curve_array' | trans | upper }}
                    </div>
                    <div class="col-md-5">
                            <span class="text-danger">
                                {{ form_errors(form.chargingCurveArray) }}
                            </span>
                                {{ form_widget(form.chargingCurveArray) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-1 btn btn-dark" role="button" href="{{ path('ev_routing_index',{'profile': profile.id, 'brand': brand}) }}">{{ 'cancel' | trans}}</a>
            {% if update == true %}
                    <a href="#" class="btn btn-danger mr-3" data-bs-toggle="modal" data-bs-target="#delete-ev-routing-modal">{{ 'btn_delete' | trans }}</a>
            {% endif %}
            <button class="btn btn-success" type="submit">{{ button_label|default('btn_save') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
{% block modals %}
    {% if update == true %}
        <div class="modal fade" id="delete-ev-routing-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
            <form action="{{ path('ev_routing_delete', {profile: profile.id, evRouting: id, brand: brand}) }}" id="delete-ev-routing-form" method="POST">
                <input type="hidden" name="token" value="{{ csrf_token('delete-ev-routing') }}"/>
                <input type="hidden" name="_method" value="DELETE">

                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="delete">{{ 'confirmation' | trans }}</h5>
                            <button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">{{ 'ev_routing_delete' | trans}}</div>
                        <div class="modal-footer">
                            <button class="btn btn-light" type="button" data-bs-dismiss="modal">{{ 'btn_cancel' | trans }}</button>
                            <button class="btn btn-danger" type="submit">{{ 'btn_delete' | trans }}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {% endif %}


{% endblock %}
