{#
    SPS Eligibility Model New Template

    This template provides a form for creating new Model-based eligibility rules
    for the SPS (Smart Phone Services) system. Model rules define eligibility
    based on vehicle models and model years.

    <AUTHOR>
    @since 2025-06-27
#}
{% extends '_layout/layout.html.twig' %}

{% block title %}{{ 'Create New Model Eligibility Rule' | trans }}{% endblock %}

{% block content %}
<div class="container-fluid">
    {# Page Header #}
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus-circle me-2"></i>{{ 'Create New Model Eligibility Rule' | trans }}
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ path('sps_eligibility_index') }}">{{ 'SPS Eligibility' | trans }}</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ path('sps_eligibility_index', {'tab': 'model'}) }}">{{ 'Model Rules' | trans }}</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">{{ 'New Rule' | trans }}</li>
            </ol>
        </nav>
    </div>

    {# Flash Messages #}
    {% for type, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{{ type == 'success' ? 'check-circle' : (type == 'error' ? 'exclamation-triangle' : 'info-circle') }} me-2"></i>
                {{ message | trans }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endfor %}

    {# Main Form Card #}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-car me-2"></i>{{ 'Model Eligibility Rule Details' | trans }}
            </h6>
        </div>
        <div class="card-body">
            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
            
            <div class="row">
                <div class="col-md-12">
                    {# Models Field #}
                    <div class="form-group mb-4">
                        {{ form_label(form.modelsString, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                        {{ form_widget(form.modelsString, {'attr': {'class': 'form-control form-control-lg'}}) }}
                        {% if form.modelsString.vars.help %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>{{ form.modelsString.vars.help }}
                            </div>
                        {% endif %}
                        {{ form_errors(form.modelsString) }}
                    </div>

                    {# Eligibility Rules Field #}
                    <div class="form-group mb-4">
                        {{ form_label(form.eligibilityRule, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                        {{ form_widget(form.eligibilityRule, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.eligibilityRule) }}
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            {# Type Field #}
                            <div class="form-group mb-4">
                                {{ form_label(form.type, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.type, {'attr': {'class': 'form-control'}}) }}
                                {% if form.type.vars.help %}
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>{{ form.type.vars.help }}
                                    </div>
                                {% endif %}
                                {{ form_errors(form.type) }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            {# Model Year From Field #}
                            <div class="form-group mb-4">
                                {{ form_label(form.modelYearFrom, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.modelYearFrom, {'attr': {'class': 'form-control'}}) }}
                                {% if form.modelYearFrom.vars.help %}
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>{{ form.modelYearFrom.vars.help }}
                                    </div>
                                {% endif %}
                                {{ form_errors(form.modelYearFrom) }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            {# Disclaimer Field #}
                            <div class="form-group mb-4">
                                {{ form_label(form.eligibilityDisclaimer, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.eligibilityDisclaimer, {'attr': {'class': 'form-control'}}) }}
                                {{ form_errors(form.eligibilityDisclaimer) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {# Form Actions #}
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between">
                        <a href="{{ path('sps_eligibility_index', {'tab': 'model'}) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>{{ 'Cancel' | trans }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>{{ 'Create Model Rule' | trans }}
                        </button>
                    </div>
                </div>
            </div>

            {{ form_end(form) }}
        </div>
    </div>


</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
{% endblock %}
