{% extends '_layout/layout.html.twig' %}

{% block content %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'SPS Eligibility Model Rule Details' | trans }}</h6>
                <div>
                    {% if can_write %}
                        <a href="{{ path('sps_eligibility_model_edit', {'id': sps_eligibility.id}) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit"></i> {{ 'Edit' | trans }}
                        </a>
                    {% endif %}
                    <a href="{{ path('sps_eligibility_model_index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ 'Back to List' | trans }}
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ 'Vehicle Models' | trans }}</h5>
                    <div class="mb-3">
                        {% for model in sps_eligibility.models %}
                            <span class="badge bg-primary me-1 mb-1">{{ model }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>{{ 'Model Year From' | trans }}</h5>
                    <p><span class="badge bg-success">{{ sps_eligibility.modelYearFrom }}</span></p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h5>{{ 'Type' | trans }}</h5>
                    <p><span class="badge bg-info">{{ sps_eligibility.type | title }}</span></p>
                </div>
                <div class="col-md-6">
                    <h5>{{ 'Disclaimer Required' | trans }}</h5>
                    <p>
                        {% if sps_eligibility.eligibilityDisclaimer %}
                            <span class="badge bg-warning">{{ 'Yes' | trans }}</span>
                        {% else %}
                            <span class="badge bg-success">{{ 'No' | trans }}</span>
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h5>{{ 'Eligibility Rule' | trans }}</h5>
                    <div class="border p-3 bg-light rounded">
                        {{ 'Vehicles with models' | trans }} 
                        {% for model in sps_eligibility.models %}
                            <strong>{{ model }}</strong>{% if not loop.last %}, {% endif %}
                        {% endfor %}
                        {{ 'and model year' | trans }} <strong>{{ sps_eligibility.modelYearFrom }}</strong> {{ 'or newer are eligible' | trans }}.
                    </div>
                </div>
            </div>

            {% if can_write %}
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#delete-rule-modal">
                                <i class="fas fa-trash"></i> {{ 'Delete Rule' | trans }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Delete Modal -->
                <div class="modal fade" id="delete-rule-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
                    <form action="{{ path('sps_eligibility_model_delete', {'id': sps_eligibility.id}) }}" method="POST">
                        <input type="hidden" name="token" value="{{ csrf_token('delete-sps-eligibility') }}">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">{{ 'Confirmation' | trans }}</h5>
                                    <button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">×</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    {{ 'Are you sure you want to delete this SPS Eligibility Model rule?' | trans }}
                                    <br><strong>{{ 'Type' | trans }}: {{ sps_eligibility.type | title }}</strong>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ 'Cancel' | trans }}</button>
                                    <button type="submit" class="btn btn-danger">{{ 'Delete' | trans }}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
