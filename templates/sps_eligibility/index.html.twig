{% extends '_layout/layout.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
    <style>
        .search-form {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }
        .lcdv-codes {
            max-width: 200px;
            word-wrap: break-word;
        }
        .eligibility-rules {
            max-width: 300px;
            word-wrap: break-word;
        }
    </style>
{% endblock %}

{% block content %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">{{ 'SPS Eligibility' | trans | upper }}</h1>
                {% if can_write %}
                    <a role="button" class="btn btn-primary" href="{{ path('sps_eligibility_new') }}">
                        <i class="fas fa-plus"></i> {{ 'Add New Rule' | trans }}
                    </a>
                {% endif %}
            </div>

            <!-- Search Form -->
            <div class="search-form">
                <form method="GET" action="{{ path('sps_eligibility_index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="lcdv_code" class="form-label">{{ 'LCDV Code' | trans }}</label>
                            <input type="text" class="form-control" id="lcdv_code" name="lcdv_code" 
                                   value="{{ search.lcdv_code }}" placeholder="Search by LCDV code">
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">{{ 'Type' | trans }}</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">{{ 'All Types' | trans }}</option>
                                {% for type_option in types %}
                                    <option value="{{ type_option }}" {% if search.type == type_option %}selected{% endif %}>
                                        {{ type_option | title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="disclaimer" class="form-label">{{ 'Disclaimer' | trans }}</label>
                            <select class="form-control" id="disclaimer" name="disclaimer">
                                <option value="">{{ 'All' | trans }}</option>
                                <option value="true" {% if search.disclaimer == 'true' %}selected{% endif %}>{{ 'Yes' | trans }}</option>
                                <option value="false" {% if search.disclaimer == 'false' %}selected{% endif %}>{{ 'No' | trans }}</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-secondary me-2">
                                <i class="fas fa-search"></i> {{ 'Search' | trans }}
                            </button>
                            <a href="{{ path('sps_eligibility_index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> {{ 'Clear' | trans }}
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%">
                    <thead>
                        <tr>
                            <th class="text">{{ 'LCDV24-4digits' | trans }}</th>
                            <th class="text">{{ 'Eligibility Rules' | trans }}</th>
                            <th class="text">{{ 'Type' | trans }}</th>
                            <th class="text">{{ 'Disclaimer' | trans }}</th>
                            <th class="text">{{ 'Created At' | trans }}</th>
                            <th class="text">{{ 'Updated At' | trans }}</th>
                            <th class="text text-end" width="20%">{{ 'Actions' | trans }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rule in eligibility_rules %}
                            <tr>
                                <td class="lcdv-codes">
                                    {% set codes = rule.lcdvCodesArray %}
                                    {% for code in codes %}
                                        {% if loop.index <= 5 %}
                                            <span class="badge bg-secondary me-1 mb-1">{{ code }}</span>
                                        {% endif %}
                                    {% endfor %}
                                    {% if codes|length > 5 %}
                                        <span class="text-muted">... and {{ codes|length - 5 }} more</span>
                                    {% endif %}
                                </td>
                                <td class="eligibility-rules">
                                    {{ rule.eligibilityRules|length > 100 ? rule.eligibilityRules|slice(0, 100) ~ '...' : rule.eligibilityRules }}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ rule.type | title }}</span>
                                </td>
                                <td class="text-center">
                                    {% if rule.eligibilityDisclaimer %}
                                        <span class="badge bg-warning">{{ 'Yes' | trans }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ 'No' | trans }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ rule.createdAt ? rule.createdAt|date('Y-m-d H:i') : '-' }}</td>
                                <td>{{ rule.updatedAt ? rule.updatedAt|date('Y-m-d H:i') : '-' }}</td>
                                <td class="text-end">
                                    <a class="btn btn-sm btn-info me-1" href="{{ path('sps_eligibility_show', { 'id': rule.id }) }}">
                                        <i class="fas fa-eye"></i> {{ 'View' | trans }}
                                    </a>
                                    {% if can_write %}
                                        <a class="btn btn-sm btn-warning me-1" href="{{ path('sps_eligibility_edit', { 'id': rule.id }) }}">
                                            <i class="fas fa-edit"></i> {{ 'Edit' | trans }}
                                        </a>
                                        <a href="#" class="btn btn-sm btn-danger"
                                           data-bs-toggle="modal"
                                           data-bs-target="#delete-rule-modal-{{ rule.id }}">
                                            <i class="fas fa-trash"></i> {{ 'Delete' | trans }}
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>

                            <!-- Delete Modal -->
                            {% if can_write %}
                                <div class="modal fade" id="delete-rule-modal-{{ rule.id }}" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
                                    <form action="{{ path('sps_eligibility_delete', {'id': rule.id}) }}" method="POST">
                                        <input type="hidden" name="token" value="{{ csrf_token('delete-sps-eligibility') }}">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">{{ 'Confirmation' | trans }}</h5>
                                                    <button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    {{ 'Are you sure you want to delete this SPS Eligibility rule?' | trans }}
                                                    <br><strong>{{ 'Type' | trans }}: {{ rule.type | title }}</strong>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ 'Cancel' | trans }}</button>
                                                    <button type="submit" class="btn btn-danger">{{ 'Delete' | trans }}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            {% endif %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center">{{ 'No SPS Eligibility rules found' | trans }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function () {
            $('#dataTable').DataTable({
                "pageLength": 25,
                "order": [[ 4, "desc" ]], // Order by created_at desc
                "columnDefs": [
                    { "orderable": false, "targets": 6 } // Disable sorting on actions column
                ]
            });
        });
    </script>
{% endblock %}
