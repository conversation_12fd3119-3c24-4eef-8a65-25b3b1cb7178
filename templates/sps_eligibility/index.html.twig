{#
    SPS Eligibility Main Template

    This template provides a tabbed interface for managing SPS (Smart Phone Services)
    eligibility rules. It includes two tabs:
    - LCDV: Rules based on 4-digit vehicle identification codes
    - Model: Rules based on vehicle models and model years

    The template uses Bootstrap tabs with URL-based state management
    and DataTables for data presentation.

    <AUTHOR> BO Development Team
    @since 2024-06-27
#}
{% extends '_layout/layout.html.twig' %}

{% block title %}{{ 'SPS Eligibility' | trans }}{% endblock %}

{% block style %}
    {# DataTables CSS for table styling #}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
    <style>
        /* Custom tab styling for SPS Eligibility interface */
        .nav-tabs .nav-link {
            color: #495057;
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #495057;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 1.5rem;
            background-color: #fff;
        }
        /* LCDV code badge styling */
        .lcdv-codes .badge {
            font-size: 0.75em;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {# Page header with title #}
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ 'SPS Eligibility' | trans }}</h1>
    </div>

    {# Bootstrap tab navigation for LCDV and Model sections #}
    <ul class="nav nav-tabs" id="spsEligibilityTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link {{ active_tab == 'lcdv' ? 'active' : '' }}"
               id="lcdv-tab"
               href="{{ path('sps_eligibility_index', {'tab': 'lcdv'}) }}"
               role="tab"
               aria-controls="lcdv"
               aria-selected="{{ active_tab == 'lcdv' ? 'true' : 'false' }}">
                <i class="fas fa-code me-2"></i>{{ 'LCDV' | trans }}
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link {{ active_tab == 'model' ? 'active' : '' }}"
               id="model-tab"
               href="{{ path('sps_eligibility_index', {'tab': 'model'}) }}"
               role="tab"
               aria-controls="model"
               aria-selected="{{ active_tab == 'model' ? 'true' : 'false' }}">
                <i class="fas fa-car me-2"></i>{{ 'Model' | trans }}
            </a>
        </li>
    </ul>

    {# Tab content containers for LCDV and Model sections #}
    <div class="tab-content" id="spsEligibilityTabContent">
        {# LCDV Tab - Rules based on 4-digit vehicle codes #}
        <div class="tab-pane fade {{ active_tab == 'lcdv' ? 'show active' : '' }}"
             id="lcdv"
             role="tabpanel"
             aria-labelledby="lcdv-tab">
            {% if active_tab == 'lcdv' %}
                {% include 'sps_eligibility/_lcdv_content.html.twig' with {
                    'eligibility_rules': lcdv_data.eligibility_rules,
                    'types': lcdv_data.types,
                    'search': lcdv_data.search,
                    'can_write': can_write,
                    'profile': profile
                } %}
            {% endif %}
        </div>

        {# Model Tab - Rules based on vehicle models and years #}
        <div class="tab-pane fade {{ active_tab == 'model' ? 'show active' : '' }}"
             id="model"
             role="tabpanel"
             aria-labelledby="model-tab">
            {% if active_tab == 'model' %}
                {% include 'sps_eligibility/_model_content.html.twig' with {
                    'eligibility_rules': model_data.eligibility_rules,
                    'types': model_data.types,
                    'models': model_data.models,
                    'search': model_data.search,
                    'can_write': can_write,
                    'profile': profile
                } %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block script %}
    {# DataTables JavaScript libraries #}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function () {
            // Initialize DataTables for both LCDV and Model tables
            $('.dataTable').DataTable({
                "pageLength": 25,                          // Show 25 rows per page
                "order": [[ 0, "asc" ]],                   // Sort by first column ascending
                "columnDefs": [
                    { "orderable": false, "targets": -1 }  // Disable sorting on actions column
                ]
            });

            // Handle tab switching with URL navigation for bookmarkable tabs
            $('#spsEligibilityTabs a').on('click', function (e) {
                e.preventDefault();
                window.location.href = $(this).attr('href');
            });
        });
    </script>
{% endblock %}
