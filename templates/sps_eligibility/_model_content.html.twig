{#
    Model Content Template

    This template renders the Model tab content for SPS Eligibility management.
    It includes:
    - Header with "Add New" button (for users with write permissions)
    - Search filters for model, type, model year, and disclaimer
    - Data table with Model eligibility rules
    - Action buttons for view/edit/delete operations

    <AUTHOR>
    @since 2024-06-27
#}

{# Header section with title and add button #}
<div class="row mb-3">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ 'Model Eligibility Rules' | trans }}</h4>
            {% if can_write %}
                <a href="{{ path('sps_eligibility_model_new') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{{ 'Add New Model Rule' | trans }}
                </a>
            {% endif %}
        </div>
    </div>
</div>

{# Search filters card #}
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-search me-2"></i>{{ 'Search Filters' | trans }}
    </div>
    <div class="card-body">
        <form method="GET" action="{{ path('sps_eligibility_index') }}">
            <input type="hidden" name="tab" value="model">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="model">{{ 'Model' | trans }}</label>
                        <select class="form-control" id="model" name="model">
                            <option value="">{{ 'All Models' | trans }}</option>
                            {% for model in models %}
                                <option value="{{ model }}" {{ search.model == model ? 'selected' : '' }}>
                                    {{ model }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="type">{{ 'Type' | trans }}</label>
                        <select class="form-control" id="type" name="type">
                            <option value="">{{ 'All Types' | trans }}</option>
                            {% for type in types %}
                                <option value="{{ type }}" {{ search.type == type ? 'selected' : '' }}>
                                    {{ type }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="model_year_from">{{ 'Model Year From' | trans }}</label>
                        <input type="number" 
                               class="form-control" 
                               id="model_year_from" 
                               name="model_year_from" 
                               value="{{ search.model_year_from }}" 
                               placeholder="{{ 'e.g. 2020' | trans }}"
                               min="1900" 
                               max="2050">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="disclaimer">{{ 'Disclaimer' | trans }}</label>
                        <select class="form-control" id="disclaimer" name="disclaimer">
                            <option value="">{{ 'All' | trans }}</option>
                            <option value="1" {{ search.disclaimer == '1' ? 'selected' : '' }}>{{ 'Yes' | trans }}</option>
                            <option value="0" {{ search.disclaimer == '0' ? 'selected' : '' }}>{{ 'No' | trans }}</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>{{ 'Search' | trans }}
                            </button>
                            <a href="{{ path('sps_eligibility_index', {'tab': 'model'}) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>{{ 'Clear' | trans }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Model Rules Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{{ 'Model Eligibility Rules' | trans }}</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered dataTable" id="modelDataTable" width="100%">
                <thead>
                    <tr>
                        <th class="text">{{ 'Model' | trans }}</th>
                        <th class="text">{{ 'Model Year From' | trans }}</th>
                        <th class="text">{{ 'Eligibility Rules' | trans }}</th>
                        <th class="text">{{ 'Type' | trans }}</th>
                        <th class="text">{{ 'Disclaimer' | trans }}</th>
                        <th class="text text-end" width="20%">{{ 'Actions' | trans }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rule in eligibility_rules %}
                        <tr>
                            <td>
                                <strong>{{ rule.model }}</strong>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-primary">{{ rule.modelYear }}</span>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 250px;" title="{{ rule.eligibilityRules }}">
                                    {{ rule.eligibilityRules }}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ rule.type }}</span>
                            </td>
                            <td class="text-center">
                                {% if rule.eligibilityDisclaimer %}
                                    <span class="badge bg-warning">{{ 'Yes' | trans }}</span>
                                {% else %}
                                    <span class="badge bg-success">{{ 'No' | trans }}</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <div class="btn-group" role="group">
                                    <a href="{{ path('sps_eligibility_model_show', {'id': rule.id}) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="{{ 'View' | trans }}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if can_write %}
                                        <a href="{{ path('sps_eligibility_model_edit', {'id': rule.id}) }}" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="{{ 'Edit' | trans }}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ path('sps_eligibility_model_delete', {'id': rule.id}) }}" 
                                           class="btn btn-sm btn-outline-danger" 
                                           title="{{ 'Delete' | trans }}"
                                           onclick="return confirm('{{ 'Are you sure you want to delete this rule?' | trans }}')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                {{ 'No model eligibility rules found.' | trans }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
