{% extends '_layout/layout.html.twig' %}

{% block stylesheets %}
    <style>
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .lcdv-badge {
            margin: 2px;
        }
    </style>
{% endblock %}

{% block content %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'SPS Eligibility Rule Details' | trans }} #{{ sps_eligibility.id }}</h6>
                <div>
                    {% if can_write %}
                        <a href="{{ path('sps_eligibility_edit', {'id': sps_eligibility.id}) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit"></i> {{ 'Edit' | trans }}
                        </a>
                        <a href="#" class="btn btn-danger me-2"
                           data-bs-toggle="modal"
                           data-bs-target="#delete-rule-modal">
                            <i class="fas fa-trash"></i> {{ 'Delete' | trans }}
                        </a>
                    {% endif %}
                    <a href="{{ path('sps_eligibility_index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ 'Back to List' | trans }}
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-4">
                        <h5 class="detail-label">{{ 'LCDV24-4digits' | trans }}</h5>
                        <div class="detail-value">
                            {% for code in sps_eligibility.lcdvCodesArray %}
                                <span class="badge bg-secondary lcdv-badge">{{ code }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5 class="detail-label">{{ 'Type' | trans }}</h5>
                        <div class="detail-value">
                            <span class="badge bg-info fs-6">{{ sps_eligibility.type | title }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5 class="detail-label">{{ 'Eligibility Disclaimer' | trans }}</h5>
                        <div class="detail-value">
                            {% if sps_eligibility.eligibilityDisclaimer %}
                                <span class="badge bg-warning fs-6">{{ 'Yes' | trans }}</span>
                            {% else %}
                                <span class="badge bg-success fs-6">{{ 'No' | trans }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="mb-4">
                        <h5 class="detail-label">{{ 'Eligibility Rules based on LCDV attribute' | trans }}</h5>
                        <div class="detail-value">
                            <div class="p-3 bg-light border rounded">
                                {{ sps_eligibility.eligibilityRules | nl2br }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <h5 class="detail-label">{{ 'Created At' | trans }}</h5>
                        <div class="detail-value">
                            {{ sps_eligibility.createdAt|date('Y-m-d H:i:s') }}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <h5 class="detail-label">{{ 'Updated At' | trans }}</h5>
                        <div class="detail-value">
                            {{ sps_eligibility.updatedAt ? sps_eligibility.updatedAt|date('Y-m-d H:i:s') : 'Never' | trans }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    {% if can_write %}
        <div class="modal fade" id="delete-rule-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
            <form action="{{ path('sps_eligibility_delete', {'id': sps_eligibility.id}) }}" method="POST">
                <input type="hidden" name="token" value="{{ csrf_token('delete-sps-eligibility') }}">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">{{ 'Confirmation' | trans }}</h5>
                            <button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            {{ 'Are you sure you want to delete this SPS Eligibility rule?' | trans }}
                            <br><strong>{{ 'Type' | trans }}: {{ sps_eligibility.type | title }}</strong>
                            <br><strong>{{ 'ID' | trans }}: {{ sps_eligibility.id }}</strong>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ 'Cancel' | trans }}</button>
                            <button type="submit" class="btn btn-danger">{{ 'Delete' | trans }}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}
