<!-- jQuery -->
<script type="text/javascript" charset="utf8" src="{{ asset('js/jquery.min.js') }}"></script>
<!-- jQuery UI -->
<script src="{{ asset('js/jquery-ui.js') }}"></script> 
<!-- Bootstrap 4 -->
<script src="{{ asset('js/datatables/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('plugins/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

<!-- Select2 -->
<script src="{{ asset('plugins/select2/js/select2.full.min.js') }}"></script>

<!-- SPACE BO Js -->
<script src="{{ asset('js/space.js') }}"></script>
<!-- AdminLTE App -->
<script src="{{ asset('js/adminlte.min.js') }}"></script>
<!-- Bootstrap4 Duallistbox -->
<script src="{{ asset('plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js') }}"></script>

<!-- DataTables  & Plugins -->
<script type="text/javascript" charset="utf8" src="{{ asset('js/datatables/jquery.dataTables.js') }}"></script>
<script type="text/javascript" charset="utf8" src="{{ asset('js/datatables/dataTables.bootstrap5.min.js') }}"></script>
<script type="text/javascript" charset="utf8" src="{{ asset('js/date-picker/flatpickr.min.js') }}"></script>
<script type="text/javascript" >
	$(function () {
		$('.select2').select2({
			theme: 'bootstrap4'
		});	

        $(".date-picker").flatpickr({enableTime: true, dateFormat: "d/m/Y H:i:S", allowInput: true, time_24hr: true});
  	})
    if(!$("#profile_login_form_brand").val()){
        $("#profile_login_form_country").prop('disabled', true);
    }
    
    $("#profile_login_form_brand").change(function (e) {
        var url = "{{path('get_brand_countries', {code: ':code'})}}"; 
        url = url.replace(':code', $(this).val());

        if($(this).val() == ''){
            return;
        }

        $.ajax({
            url,
            type: 'get',
            dataType: 'json',
            success: function (response) {
                $("#profile_login_form_country").empty();
                $("#profile_login_form_country").append("<option value>{{ 'choose_country'|trans }}</option>");
                $.each(response, function (i, country) {
                    $("#profile_login_form_country").append("<option value='" + country.code + "'>" + country.name + "</option>");
                });
                 $("#profile_login_form_country").prop('disabled', false);
            }, 
            error: function (response) {
                console.log(response);
                $("#profile_login_form_country").prop('disabled', true);
            }
        });
    });

    $("#profile_login_form_profile").change(function (e) {
        var url = "{{path('get_brand_and_country', {profile: ':profile'})}}";
        url = url.replace(':profile', $(this).val());
        if($(this).val() == ''){
            $("#profile_login_form_brand").val(null);
            $("#profile_login_form_country").val(null);
            $("#profile_login_form_country").prop('disabled', true);
            return;
        }

        $.ajax({
            url,
            type: 'get',
            dataType: 'json',
            success: function (response) {
                if(response.length == 0){
                    $("#profile_login_form_brand").val(null);
                    $("#profile_login_form_country").val(null);
                    return;
                }
                $("#profile_login_form_brand").val(response.brandCode);
                $("#profile_login_form_country").val(response.countryCode);
                $("#profile_login_form_country").prop('disabled', false);
            }, 
            error: function (response) {
                console.log('error',response);
            }
        });
    });

    $(document).ready(function() {
        if($("#profile_login_form_brand").val() || $("#profile_login_form_country").val()){
            filter(jQuery('#profile_login_form_brand').val(), jQuery('#profile_login_form_country').val())
        }


    });

    $(function () {
        //Bootstrap Duallistbox
        $(".duallistbox").each(function () {
            $(this).bootstrapDualListbox({
                infoText: false,
                filterPlaceHolder: "{{ 'filter_place_holder'|trans }}",
                btnMoveAllText: $(this).attr('moveAll'),
                btnRemoveAllText: $(this).attr('removeAll')
            })
        })

        $("input[data-bootstrap-switch]").each(function () {
            $(this).bootstrapSwitch('state', $(this).prop('checked'));
        })
    });

    $(document).ready(function() {
        menu_list = getMenusList();

        $("#search-bar").focusin(function() {
            $('#search-error').text('').hide();
        });
        $("#search-bar").autocomplete({
            source: menu_list,
            html:true
        }).data("ui-autocomplete")._renderItem = function (ul, item) {
            isFavoriteExist = isInFavoriteList(item.id);
            starStyle = (isFavoriteExist) ? 'fas fa-star' : 'far fa-star';
            action = (isFavoriteExist) ? 'delete' : 'add';
            return $("<li class='result-item'></li>")
                .data("item.autocomplete", item)
                .append("<a href='" + item.path + "'>" + item.label + '</a> <i class="nav-icon ' + starStyle + '" onclick="updateFavorite(\'' + item.id + '\', \'' + action + '\');return;"></i>')
                .appendTo(ul);
        };
    });

    function updateFavorite(menu_id, action) {
        var items = menu_id.split('_');
        menu_id = items[2];
        showLoader('#favorites-list');
        $.post( "/favorite/update/" + menu_id + "/" + action, function() {})
        .done(function ( data ) {
            if(data.status != 'success') {
                $('#search-error').text(data.message).show();
                hideLoader();
                return;
            }
            if(data.action == 'add') {
                var element_id = "#menu_item_" + menu_id;
                $(element_id).parent().clone().appendTo("#favorites-list .sub_menu").fadeIn('slow');
                var element_new_id = element_id.replace('#menu', 'favorite');
                $(element_id).attr('id', element_new_id);
                if ($('#' + element_new_id).hasClass("active-link")) {
                    $('#' + element_new_id).removeClass("active-link");
                }
                $('#' + element_new_id).append('<i class="fa fa-star favorite-icon" onclick="updateFavorite(\'' + element_new_id + '\', \'delete\');return false;"></i>');
                
            } else if(data.action == 'delete') {
                $("#favorites-list #favorite_item_" + menu_id).parent().remove();
            }
             $("#search-bar").val('');
            hideLoader();

        })
        .fail(function() {
            hideLoader();
        });
    }

    function getMenusList() {
        var menu_list = [];
        $('#menus-list .link-menu').each(function () {           
            if($(this).next('div').length == 0 ) {
                value = {
                    id: $(this).attr('id'),
                    path: $(this).attr('href'),
                    label: $(this).text(),
                };
                menu_list.push(value);                             
            }
         });  
        return menu_list;
    }
   

    function isInFavoriteList(item_id) {
        if(item_id !== undefined) {
            item_id = item_id.replace('menu', 'favorite');
            if($('#' + item_id).length) {
                return true
            }
        }
        return false;
    }

    const translations = {
        dashboard: '{{ 'dashboard'|trans }}'
    };

    document.addEventListener("DOMContentLoaded", function() {
        var currentPathname = window.location.pathname;
        var selectedMenuName = '';
        var bestMatch = null;
        var bestMatchLength = 0;

        $('#menus-list .sub-link').each(function() {
            var menuHref = $(this).attr('href');

            // Extract pathname from menu href (remove query parameters and hash)
            var menuPathname = menuHref;
            if (menuHref.includes('?')) {
                menuPathname = menuHref.split('?')[0];
            }
            if (menuPathname.includes('#')) {
                menuPathname = menuPathname.split('#')[0];
            }

            // Exact match has highest priority
            if (menuPathname === currentPathname) {
                $('.sub-link').removeClass('active-menu');
                $(this).addClass('active-menu');
                selectedMenuName = $(this).text();
                return false;
            }

            // Enhanced path matching logic to prevent conflicts between similar routes
            var isValidMatch = false;

            // Check if current path starts with menu pathname (without query params)
            if (currentPathname.startsWith(menuPathname)) {
                // For admin routes, ensure we have a proper path segment match
                if (menuPathname.startsWith('/admin/')) {
                    var menuSegments = menuPathname.split('/').filter(segment => segment.length > 0);
                    var currentSegments = currentPathname.split('/').filter(segment => segment.length > 0);

                    // Check if all menu segments match the beginning of current segments
                    if (menuSegments.length <= currentSegments.length) {
                        isValidMatch = true;
                        for (var i = 0; i < menuSegments.length; i++) {
                            if (menuSegments[i] !== currentSegments[i]) {
                                isValidMatch = false;
                                break;
                            }
                        }

                        // Additional check: ensure we don't have partial matches
                        // For example, /admin/sps should not match /admin/sps-eligibility
                        if (isValidMatch && menuSegments.length < currentSegments.length) {
                            var nextCurrentSegment = currentSegments[menuSegments.length];
                            var lastMenuSegment = menuSegments[menuSegments.length - 1];

                            // If the next segment starts with the last menu segment followed by a dash,
                            // this is likely a different route (e.g., sps vs sps-eligibility)
                            if (nextCurrentSegment && nextCurrentSegment.startsWith(lastMenuSegment + '-')) {
                                isValidMatch = false;
                            }
                        }
                    }
                } else {
                    // For non-admin routes, use the original logic
                    isValidMatch = true;
                }

                // Only consider this a match if it's valid and longer than current best
                // Use menuPathname length for comparison to ignore query parameters
                if (isValidMatch && menuPathname.length > bestMatchLength) {
                    bestMatch = $(this);
                    bestMatchLength = menuPathname.length;
                    selectedMenuName = $(this).text();
                }
            }

            // Special handling for profile routes
            if (menuHref.split("/")[1] === 'profile' && currentPathname.split("/")[1] === 'profile') {
                if (menuHref.split("/")[2] === currentPathname.split("/")[2]) {
                    $('.sub-link').removeClass('active-menu');
                    $(this).addClass('active-menu');
                    selectedMenuName = $(this).text();
                    return false;
                }
            }
        });

        // Apply the best match if no exact match was found
        if (bestMatch && bestMatchLength > 0) {
            $('.sub-link').removeClass('active-menu');
            bestMatch.addClass('active-menu');
        }
         
        if(selectedMenuName == 'null' || selectedMenuName == '')
        {
            selectedMenuName = translations.dashboard;
        }
        $('#selected-menu').text(selectedMenuName);
    }); 

    $(document).ready(function() {
        $('form').on('submit', function () {
            const submitButton = $(this).find('button[type=submit]');
            const submitHtml   = submitButton.html()
            submitButton.prop('disabled', true).html("Saving...");
            sessionStorage.setItem('formSubmitted', 'true');

            setTimeout(function () {
                // Re-enable the button and reset the text after a delay
                submitButton.prop('disabled', false).html(submitHtml);
                sessionStorage.setItem('formSubmitted', 'false');
            }, 5000);
        });
    });
    

</script>
