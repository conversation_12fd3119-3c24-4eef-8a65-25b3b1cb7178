{% macro buildSubMenus(menus, role, menusIds) %}
	<div id="menus-list" class="list-unstyled">
		{% for subMenu in menus %}
			{% if subMenu.id in menusIds %}
				<div class="sub-menu-link">
					{% set subMenuRouteName =  subMenu.routeName | trim | trim('#') %}
					{% if subMenu.feature %}
						{% set path = route_exists(subMenuRouteName) ? path(subMenu.routeName, {'id': subMenu.feature.id}) : '#' %}
						{% set activeLink = path == app.request.get('currentUrl') %}
					{% else %}
						{% set path = route_exists(subMenuRouteName) ? path(subMenu.routeName) : '#' %}
						{% set currentUrl = app.request.get('currentUrl') %}
						{% set activeLink = false %}

						{# Improved path matching logic to prevent conflicts between similar routes #}
						{% if path != '#' and currentUrl %}
							{# Remove query parameters and hash from both URLs #}
							{% set cleanPath = path|split('?')[0]|split('#')[0] %}
							{% set cleanCurrentUrl = currentUrl|split('?')[0]|split('#')[0] %}

							{# Check for exact match first #}
							{% if cleanPath == cleanCurrentUrl %}
								{% set activeLink = true %}
							{# Check for valid prefix match #}
							{% elseif cleanCurrentUrl starts with cleanPath %}
								{# Ensure complete path segment match, not partial #}
								{% set remainingPath = cleanCurrentUrl|slice(cleanPath|length) %}
								{% if cleanPath ends with '/' or remainingPath == '' or remainingPath starts with '/' %}
									{% set activeLink = true %}
								{% endif %}
							{% endif %}
						{% endif %}
					{% endif %}
					<a href="{{path}}" class="link-menu sub-link {% if subMenu.subMenus|length %}parent{% else %}{% if path == '#' %} disabled-link{% endif %}{% endif %}{% if activeLink and subMenu.subMenus|length == 0 %} active-link{% endif %}" id="menu_item_{{ subMenu.id }}" {% if subMenu.routeName is null %} data-toggle="collapse" aria-expanded="true" {% endif %}>
						<div>
							<i class="menu-icon {{ subMenu.iconClass ? subMenu.iconClass : 'fas fa-tag' }}"></i>
							<span id="word-prevent">{{ subMenu.label|trans }}</span>
						</div>
						{% if subMenu.subMenus|length %}
							<i class="fas fa-chevron-up chevron-icon fa-xs"></i>
						{% endif %}
					</a>
					{% if subMenu.subMenus|length > 0 %}
						<div class="collapse ps-2 sub_menu">
							{{ _self.buildSubMenus(subMenu.subMenus, role, menusIds) }}
						</div>
					{% endif %}
				</div>
			{% endif %}
		{% endfor %}
	</div>
{% endmacro %}
<div class="scrollable">
	<div class="list-unstyled bloc-menu">
		{% include "_layout/favorite.html.twig" %}
		{% for menu in menus %}
			{% if menu.parent is null and menu.id in menusIds %}
				<div class="bloc-link">
					{% set menuRouteName =  menu.routeName | trim | trim('#') %}
					{% if menu.feature %}
						{% set path = menuRouteName ? path(menu.routeName, {'id': menu.feature.id} ) : '#'%}
					{% else %}
						{% set path = route_exists(menuRouteName) ? path(menu.routeName, routeArray(menu.parameters)) : '#' %}
					{% endif %}
					<a href="{{path}}" class="link-menu {% if menu.subMenus|length %}parent{% else %}{% if path == '#' %} disabled-link{% endif %}{% endif %}" {% if menu.routeName is null %} data-toggle="collapse" aria-expanded="true" {% endif %}>
						<div>
							<i class="menu-icon {{ menu.iconClass ? menu.iconClass : 'fas fa-tag' }}"></i>
							{{ menu.label|trans }}
						</div>
						{% if menu.subMenus|length %}
							<i class="fas fa-chevron-up chevron-icon fa-xs"></i>
						{% endif %}
					</a>
					{% if menu.subMenus|length %}
						<div class="collapse sub_menu" id="parent_{{ menu.id }}">
							{{ _self.buildSubMenus(menu.subMenus, role, menusIds) }}
						</div>
					{% endif %}
				</div>
			{% endif %}
		{% endfor %}
	</div>
</div>