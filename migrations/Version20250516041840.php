<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250516041840 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE local_key_json_release CHANGE old_filename old_filename VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE local_translation RENAME INDEX idx_e6f7fb05d07ed992 TO IDX_DF6037C1D07ED992');
        $this->addSql('ALTER TABLE local_translation RENAME INDEX idx_e6f7fb05f6bd1646 TO IDX_DF6037C1F6BD1646');
        $this->addSql('ALTER TABLE local_translation RENAME INDEX idx_e6f7fb0582f1baf4 TO IDX_DF6037C182F1BAF4');
        $this->addSql('ALTER TABLE media_directory DROP FOREIGN KEY media_directory_ibfk_1');
        $this->addSql('ALTER TABLE media_directory ADD CONSTRAINT FK_6318F754F6BD1646 FOREIGN KEY (site_id) REFERENCES site (id)');
        $this->addSql('ALTER TABLE profile DROP FOREIGN KEY profile_ibfk_1');
        $this->addSql('ALTER TABLE profile ADD CONSTRAINT FK_8157AA0FF6BD1646 FOREIGN KEY (site_id) REFERENCES site (id)');
        $this->addSql('ALTER TABLE rpo_ev_routing ADD CONSTRAINT FK_D12B5AB587601603 FOREIGN KEY (ev_routing_id) REFERENCES ev_routing (id)');
        $this->addSql('ALTER TABLE rpo_label ADD CONSTRAINT FK_DCAF4FB6EEAB20AD FOREIGN KEY (vehicle_label_id) REFERENCES vehicle_label (id)');
        $this->addSql('DROP INDEX unique_label ON site');
        $this->addSql('ALTER TABLE vehicle_label CHANGE vl_language_id vl_language_id INT NOT NULL DEFAULT 1');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_85F91ED087FBDB6C ON widget (wguid)');
        $this->addSql('ALTER TABLE widget_feature_attribute CHANGE type type VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE messenger_messages CHANGE created_at created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE available_at available_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE delivered_at delivered_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE local_key_json_release CHANGE old_filename old_filename VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE local_translation RENAME INDEX idx_df6037c1d07ed992 TO IDX_E6F7FB05D07ED992');
        $this->addSql('ALTER TABLE local_translation RENAME INDEX idx_df6037c1f6bd1646 TO IDX_E6F7FB05F6BD1646');
        $this->addSql('ALTER TABLE local_translation RENAME INDEX idx_df6037c182f1baf4 TO IDX_E6F7FB0582F1BAF4');
        $this->addSql('ALTER TABLE media_directory DROP FOREIGN KEY FK_6318F754F6BD1646');
        $this->addSql('ALTER TABLE media_directory ADD CONSTRAINT media_directory_ibfk_1 FOREIGN KEY (site_id) REFERENCES site (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE messenger_messages CHANGE created_at created_at DATETIME NOT NULL, CHANGE available_at available_at DATETIME NOT NULL, CHANGE delivered_at delivered_at DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE profile DROP FOREIGN KEY FK_8157AA0FF6BD1646');
        $this->addSql('ALTER TABLE profile ADD CONSTRAINT profile_ibfk_1 FOREIGN KEY (site_id) REFERENCES site (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE rpo_ev_routing DROP FOREIGN KEY FK_D12B5AB587601603');
        $this->addSql('ALTER TABLE rpo_label DROP FOREIGN KEY FK_DCAF4FB6EEAB20AD');
        $this->addSql('CREATE UNIQUE INDEX unique_label ON site (label)');
        $this->addSql('ALTER TABLE vehicle_label CHANGE vl_language_id vl_language_id INT DEFAULT 1 NOT NULL');
        $this->addSql('DROP INDEX UNIQ_85F91ED087FBDB6C ON widget');
        $this->addSql('ALTER TABLE widget_feature_attribute CHANGE type type VARCHAR(255) DEFAULT NULL');
    }
}
