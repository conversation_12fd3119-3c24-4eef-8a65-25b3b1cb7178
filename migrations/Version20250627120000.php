<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Update SPS Eligibility menu structure to use tabbed interface
 *
 * This migration consolidates the SPS Eligibility menu structure from separate
 * submenu items to a unified tabbed interface:
 * - Removes submenu items for "Table 1 (LCDV)" and "Table 2 (Model)"
 * - Updates parent "SPS Eligibility" menu to have direct route
 * - Preserves role permissions for the consolidated menu
 * - Maintains access control for Product Managers (write) and Brands (read-only)
 *
 * <AUTHOR> Shrivastava
 * @since 2024-06-27
 */
final class Version20250627120000 extends AbstractMigration
{
    /**
     * Get migration description
     *
     * @return string Migration description
     */
    public function getDescription(): string
    {
        return 'Update SPS Eligibility menu structure to use tabbed interface';
    }

    /**
     * Execute the migration to consolidate SPS Eligibility menu structure
     *
     * @param Schema $schema Database schema (unused in this migration)
     */
    public function up(Schema $schema): void
    {
        // Step 1: Remove role_menu entries for submenu items to prevent foreign key constraints
        $this->addSql("DELETE FROM role_menu WHERE menu_id IN (
            SELECT id FROM menu WHERE route_name IN ('sps_eligibility_lcdv_index', 'sps_eligibility_model_index')
        )");

        // Step 2: Remove the obsolete submenu items for Table 1 (LCDV) and Table 2 (Model)
        $this->addSql("DELETE FROM menu WHERE route_name = 'sps_eligibility_lcdv_index'");
        $this->addSql("DELETE FROM menu WHERE route_name = 'sps_eligibility_model_index'");

        // Step 3: Update parent SPS Eligibility menu to have direct route for tabbed interface
        $this->addSql("UPDATE menu SET route_name = 'sps_eligibility_index' WHERE label = 'SPS Eligibility' AND route_name IS NULL");
    }

    /**
     * Reverse the migration by removing SPS Eligibility menu structure
     *
     * @param Schema $schema Database schema (unused in this migration)
     */
    public function down(Schema $schema): void
    {
        // First, remove the direct route from parent menu
        $this->addSql("UPDATE menu SET route_name = NULL WHERE label = 'SPS Eligibility' AND route_name = 'sps_eligibility_index'");

        // Get the parent menu ID for SPS Eligibility
        $parentMenuResult = $this->connection->executeQuery("SELECT id FROM menu WHERE label = 'SPS Eligibility'");
        $parentMenuId = $parentMenuResult->fetchOne();

        if ($parentMenuId) {
            // Re-create the submenu items
            $this->addSql("INSERT INTO menu (name, route_name, icon_class, parent_id, position, created_at, updated_at) VALUES
                ('Table 1 (LCDV)', 'sps_eligibility_lcdv_index', 'fas fa-code', {$parentMenuId}, 1, NOW(), NOW())");

            $this->addSql("INSERT INTO menu (name, route_name, icon_class, parent_id, position, created_at, updated_at) VALUES
                ('Table 2 (Model)', 'sps_eligibility_model_index', 'fas fa-car', {$parentMenuId}, 2, NOW(), NOW())");

            // Get the newly created menu IDs
            $lcdvMenuResult = $this->connection->executeQuery("SELECT id FROM menu WHERE route_name = 'sps_eligibility_lcdv_index'");
            $lcdvMenuId = $lcdvMenuResult->fetchOne();

            $modelMenuResult = $this->connection->executeQuery("SELECT id FROM menu WHERE route_name = 'sps_eligibility_model_index'");
            $modelMenuId = $modelMenuResult->fetchOne();
            
            if ($lcdvMenuId && $modelMenuId) {
                // Re-create role permissions for both submenus
                // Product Managers (Super Admin, Technical Admin, Functional Admin) - Full access
                $this->addSql("INSERT INTO role_menu (role_id, menu_id, created_at, updated_at) VALUES 
                    (1, {$lcdvMenuId}, NOW(), NOW()),
                    (2, {$lcdvMenuId}, NOW(), NOW()),
                    (3, {$lcdvMenuId}, NOW(), NOW()),
                    (1, {$modelMenuId}, NOW(), NOW()),
                    (2, {$modelMenuId}, NOW(), NOW()),
                    (3, {$modelMenuId}, NOW(), NOW())");
                
                // Brands (Reader, Operations, Webmaster, Local Technical Admin) - Read-only access
                $this->addSql("INSERT INTO role_menu (role_id, menu_id, created_at, updated_at) VALUES 
                    (4, {$lcdvMenuId}, NOW(), NOW()),
                    (5, {$lcdvMenuId}, NOW(), NOW()),
                    (6, {$lcdvMenuId}, NOW(), NOW()),
                    (7, {$lcdvMenuId}, NOW(), NOW()),
                    (4, {$modelMenuId}, NOW(), NOW()),
                    (5, {$modelMenuId}, NOW(), NOW()),
                    (6, {$modelMenuId}, NOW(), NOW()),
                    (7, {$modelMenuId}, NOW(), NOW())");
            }
        }
    }
}
