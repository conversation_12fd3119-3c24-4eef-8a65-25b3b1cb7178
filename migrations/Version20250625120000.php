<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration to add SPS Eligibility menu items for Table 1 (LCDV) and Table 2 (Model)
 */
final class Version20250625120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add SPS Eligibility menu items for LCDV and Model tables';
    }

    public function up(Schema $schema): void
    {
        // Get the Administration menu ID (parent_id = 2)
        $this->addSql("SET @ADMIN_MENU_ID = (SELECT id FROM `menu` WHERE label = 'menu_admin');");

        // Create parent menu for SPS Eligibility under Administration
        $this->addSql("
            INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`)
            VALUES (@ADMIN_MENU_ID, 'SPS Eligibility', 'fas fa-shield-alt', NULL, NULL);
        ");

        // Get the parent menu ID
        $this->addSql("SET @SPS_ELIGIBILITY_PARENT_ID = LAST_INSERT_ID();");

        // Create submenu for Table 1 (LCDV)
        $this->addSql("
            INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`)
            VALUES (@SPS_ELIGIBILITY_PARENT_ID, 'Table 1 (LCDV)', 'far fa-circle', 'sps_eligibility_lcdv_index', NULL);
        ");

        // Create submenu for Table 2 (Model)
        $this->addSql("
            INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`)
            VALUES (@SPS_ELIGIBILITY_PARENT_ID, 'Table 2 (Model)', 'far fa-circle', 'sps_eligibility_model_index', NULL);
        ");

        // Get menu IDs for role assignments
        $this->addSql("SET @SPS_PARENT_MENU_ID = @SPS_ELIGIBILITY_PARENT_ID;");
        $this->addSql("SET @SPS_LCDV_MENU_ID = (SELECT id FROM `menu` WHERE route_name = 'sps_eligibility_lcdv_index');");
        $this->addSql("SET @SPS_MODEL_MENU_ID = (SELECT id FROM `menu` WHERE route_name = 'sps_eligibility_model_index');");

        // Get role IDs - Based on requirements: Product Managers (write), Brands (read)
        $this->addSql("SET @SUPER_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Super Administrator');");
        $this->addSql("SET @TECH_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Technical Administrator');");
        $this->addSql("SET @FUNC_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Functional Administrator');");
        $this->addSql("SET @BRAND_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Brand Administrator');");

        // Assign permissions to Super Administrator (Write access - Product Manager role)
        $this->addSql("
            INSERT INTO `role_menu` (`role_id`, `menu_id`, `permission`) VALUES
            (@SUPER_ADMIN_ID, @SPS_PARENT_MENU_ID, 'W'),
            (@SUPER_ADMIN_ID, @SPS_LCDV_MENU_ID, 'W'),
            (@SUPER_ADMIN_ID, @SPS_MODEL_MENU_ID, 'W');
        ");

        // Assign permissions to Technical Administrator (Write access - Product Manager role)
        $this->addSql("
            INSERT INTO `role_menu` (`role_id`, `menu_id`, `permission`) VALUES
            (@TECH_ADMIN_ID, @SPS_PARENT_MENU_ID, 'W'),
            (@TECH_ADMIN_ID, @SPS_LCDV_MENU_ID, 'W'),
            (@TECH_ADMIN_ID, @SPS_MODEL_MENU_ID, 'W');
        ");

        // Assign permissions to Functional Administrator (Write access - Product Manager role)
        $this->addSql("
            INSERT INTO `role_menu` (`role_id`, `menu_id`, `permission`) VALUES
            (@FUNC_ADMIN_ID, @SPS_PARENT_MENU_ID, 'W'),
            (@FUNC_ADMIN_ID, @SPS_LCDV_MENU_ID, 'W'),
            (@FUNC_ADMIN_ID, @SPS_MODEL_MENU_ID, 'W');
        ");

        // Assign permissions to Brand Administrator (Read access - Brand role)
        $this->addSql("
            INSERT INTO `role_menu` (`role_id`, `menu_id`, `permission`) VALUES
            (@BRAND_ADMIN_ID, @SPS_PARENT_MENU_ID, 'R'),
            (@BRAND_ADMIN_ID, @SPS_LCDV_MENU_ID, 'R'),
            (@BRAND_ADMIN_ID, @SPS_MODEL_MENU_ID, 'R');
        ");
    }

    public function down(Schema $schema): void
    {
        // Remove role_menu entries
        $this->addSql("
            DELETE rm FROM role_menu rm 
            INNER JOIN menu m ON rm.menu_id = m.id 
            WHERE m.route_name IN ('sps_eligibility_lcdv_index', 'sps_eligibility_model_index') 
            OR m.label = 'SPS Eligibility';
        ");

        // Remove menu entries
        $this->addSql("
            DELETE FROM menu 
            WHERE route_name IN ('sps_eligibility_lcdv_index', 'sps_eligibility_model_index') 
            OR label = 'SPS Eligibility';
        ");
    }
}
