<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250624120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create sps_eligibility table for SPS Eligibility functionality';
    }

    public function up(Schema $schema): void
    {
        // Create sps_eligibility table
        $this->addSql('CREATE TABLE sps_eligibility (
            id INT AUTO_INCREMENT NOT NULL,
            lcdv_codes VARCHAR(255) NOT NULL,
            eligibility_rules LONGTEXT NOT NULL,
            type VARCHAR(255) NOT NULL,
            eligibility_disclaimer TINYINT(1) NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL,
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add SPS Eligibility menu entry
        $this->addSql("
             SELECT id INTO @ACCESS_MENU_ID FROM `menu` WHERE label = 'menu_admin';

             INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`)
             VALUES (NULL, @ACCESS_MENU_ID, 'SPS Eligibility','fas fa-shield-alt', 'sps_eligibility_index', NULL, NULL);

             SELECT id INTO @SPS_ELIGIBILITY_MENU_ID FROM `menu` WHERE label = 'SPS Eligibility';

             SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';
             SELECT id INTO @TECHNICAL_ADMIN_ID FROM `role` WHERE label = 'Technical Administrator';
             SELECT id INTO @FUNCTIONAL_ADMIN_ID FROM `role` WHERE label = 'Functional Administrator';

             INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
             VALUES (NULL, @SUPER_ADMIN_ID, @SPS_ELIGIBILITY_MENU_ID, 'W'),
                    (NULL, @TECHNICAL_ADMIN_ID, @SPS_ELIGIBILITY_MENU_ID, 'R'),
                    (NULL, @FUNCTIONAL_ADMIN_ID, @SPS_ELIGIBILITY_MENU_ID, 'R');
        ");
    }

    public function down(Schema $schema): void
    {
        // Remove SPS Eligibility menu entry
        $this->addSql("
            SELECT id INTO @SPS_ELIGIBILITY_MENU_ID FROM `menu` WHERE label = 'SPS Eligibility';

            DELETE FROM `role_menu` WHERE menu_id = @SPS_ELIGIBILITY_MENU_ID;

            DELETE FROM `menu` WHERE id = @SPS_ELIGIBILITY_MENU_ID;
        ");

        // Drop sps_eligibility table
        $this->addSql('DROP TABLE sps_eligibility');
    }
}
