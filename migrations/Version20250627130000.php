<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * SPS Eligibility Menu and Permissions Setup
 *
 * This migration adds the SPS Eligibility menu structure and role permissions
 * for the unified tabbed interface. It creates:
 * - Parent menu "SPS Eligibility" under Administration
 * - Direct route to the tabbed interface (sps_eligibility_index)
 * - Role permissions for Product Managers (write) and Brands (read-only)
 *
 * Access Control:
 * - Super Administrator, Technical Administrator, Functional Administrator: Write access
 * - Brand Administrator: Read-only access
 *
 * <AUTHOR> Shrivastava
 * @since 2025-06-27
 */
final class Version20250627130000 extends AbstractMigration
{
    /**
     * Get migration description
     *
     * @return string Migration description
     */
    public function getDescription(): string
    {
        return 'Add SPS Eligibility menu and permissions for unified tabbed interface';
    }

    /**
     * Execute the migration to add SPS Eligibility menu and permissions
     *
     * @param Schema $schema Database schema (unused in this migration)
     */
    public function up(Schema $schema): void
    {
        // Get the Administration menu ID
        $this->addSql("SET @ADMIN_MENU_ID = (SELECT id FROM `menu` WHERE label = 'menu_admin');");

        // Create parent menu for SPS Eligibility under Administration with direct route
        $this->addSql("
            INSERT INTO `menu` (`parent_id`, `label`, `icon_class`, `route_name`, `parameters`)
            VALUES (@ADMIN_MENU_ID, 'SPS Eligibility', 'fas fa-shield-alt', 'sps_eligibility_index', NULL);
        ");

        // Get the SPS Eligibility menu ID for role assignments
        $this->addSql("SET @SPS_MENU_ID = LAST_INSERT_ID();");

        // Get role IDs for permission assignments
        $this->addSql("SET @SUPER_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Super Administrator');");
        $this->addSql("SET @TECH_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Technical Administrator');");
        $this->addSql("SET @FUNC_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Functional Administrator');");
        $this->addSql("SET @BRAND_ADMIN_ID = (SELECT id FROM `role` WHERE label = 'Brand Administrator');");

        // Assign Write permissions to Product Manager roles (Super, Technical, Functional Administrators)
        $this->addSql("
            INSERT INTO `role_menu` (`role_id`, `menu_id`, `permission`) VALUES
            (@SUPER_ADMIN_ID, @SPS_MENU_ID, 'W'),
            (@TECH_ADMIN_ID, @SPS_MENU_ID, 'W'),
            (@FUNC_ADMIN_ID, @SPS_MENU_ID, 'W');
        ");

        // Assign Read-only permissions to Brand role (Brand Administrator)
        $this->addSql("
            INSERT INTO `role_menu` (`role_id`, `menu_id`, `permission`) VALUES
            (@BRAND_ADMIN_ID, @SPS_MENU_ID, 'R');
        ");
    }
}
