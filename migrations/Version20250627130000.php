<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * SPS Eligibility Menu and Permissions Setup
 *
 * This migration adds the SPS Eligibility menu structure and role permissions
 * for the unified tabbed interface. It creates:
 * - Parent menu "SPS Eligibility" under Administration
 * - Direct route to the tabbed interface (sps_eligibility_index)
 * - Role permissions for Product Managers (write) and Brands (read-only)
 *
 * Access Control:
 * - Super Administrator, Technical Administrator, Functional Administrator: Write access
 * - Reader: Read-only access (Brand users)
 *
 * <AUTHOR> Shrivastava
 * @since 2025-06-27
 */
final class Version20250627130000 extends AbstractMigration
{
    /**
     * Get migration description
     *
     * @return string Migration description
     */
    public function getDescription(): string
    {
        return 'Add SPS Eligibility menu and permissions for unified tabbed interface';
    }

    /**
     * Execute the migration to add SPS Eligibility menu and permissions
     *
     * @param Schema $schema Database schema (unused in this migration)
     */
    public function up(Schema $schema): void
    {
        // Add SPS Eligibility menu and permissions using the same pattern as other migrations
        $this->addSql("
            SELECT id INTO @ADMIN_MENU_ID FROM `menu` WHERE label = 'menu_admin';

            INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`)
            VALUES (NULL, @ADMIN_MENU_ID, 'SPS Eligibility', 'fas fa-shield-alt', 'sps_eligibility_index', NULL, NULL);

            SELECT id INTO @SPS_MENU_ID FROM `menu` WHERE label = 'SPS Eligibility';

            SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';
            SELECT id INTO @TECH_ADMIN_ID FROM `role` WHERE label = 'Technical Administrator';
            SELECT id INTO @FUNC_ADMIN_ID FROM `role` WHERE label = 'Functional Administrator';
            SELECT id INTO @READER_ID FROM `role` WHERE label = 'Reader';

            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            VALUES (NULL, @SUPER_ADMIN_ID, @SPS_MENU_ID, 'W');

            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            VALUES (NULL, @TECH_ADMIN_ID, @SPS_MENU_ID, 'W');

            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            VALUES (NULL, @FUNC_ADMIN_ID, @SPS_MENU_ID, 'W');

            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            VALUES (NULL, @READER_ID, @SPS_MENU_ID, 'R');
        ");
    }
}
