<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * SPS Eligibility Menu and Permissions Setup
 *
 * This migration adds the SPS Eligibility menu structure and role permissions
 * for the unified tabbed interface. It creates:
 * - Parent menu "SPS Eligibility" under Administration
 * - Direct route to the tabbed interface (sps_eligibility_index)
 * - Role permissions for Product Managers (write) and Brands (read-only)
 *
 * Access Control:
 * - Super Administrator, Technical Administrator, Functional Administrator: Write access
 * - Reader: Read-only access (Brand users)
 *
 * <AUTHOR> Shrivastava
 * @since 2025-06-27
 */
final class Version20250627130000 extends AbstractMigration
{
    /**
     * Get migration description
     *
     * @return string Migration description
     */
    public function getDescription(): string
    {
        return 'Add SPS Eligibility menu and permissions for unified tabbed interface';
    }

    /**
     * Execute the migration to add SPS Eligibility menu and permissions
     *
     * @param Schema $schema Database schema (unused in this migration)
     */
    public function up(Schema $schema): void
    {
        // Add SPS Eligibility menu under Administration
        $this->addSql("
            INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`)
            SELECT NULL, m.id, 'SPS Eligibility', 'fas fa-shield-alt', 'sps_eligibility_index', NULL, NULL
            FROM `menu` m WHERE m.label = 'menu_admin'
        ");

        // Add permissions for Super Administrator
        $this->addSql("
            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            SELECT NULL, r.id, m.id, 'W'
            FROM `role` r, `menu` m
            WHERE r.label = 'Super Administrator' AND m.label = 'SPS Eligibility'
        ");

        // Add permissions for Technical Administrator
        $this->addSql("
            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            SELECT NULL, r.id, m.id, 'W'
            FROM `role` r, `menu` m
            WHERE r.label = 'Technical Administrator' AND m.label = 'SPS Eligibility'
        ");

        // Add permissions for Functional Administrator
        $this->addSql("
            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            SELECT NULL, r.id, m.id, 'W'
            FROM `role` r, `menu` m
            WHERE r.label = 'Functional Administrator' AND m.label = 'SPS Eligibility'
        ");

        // Add read-only permissions for Reader (Brand users)
        $this->addSql("
            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            SELECT NULL, r.id, m.id, 'R'
            FROM `role` r, `menu` m
            WHERE r.label = 'Reader' AND m.label = 'SPS Eligibility'
        ");
    }

    /**
     * Reverse the migration by removing SPS Eligibility menu and permissions
     *
     * @param Schema $schema Database schema (unused in this migration)
     */
    public function down(Schema $schema): void
    {
        // Remove role permissions for SPS Eligibility menu
        $this->addSql("
            DELETE rm FROM `role_menu` rm
            INNER JOIN `menu` m ON rm.menu_id = m.id
            WHERE m.label = 'SPS Eligibility'
        ");

        // Remove SPS Eligibility menu
        $this->addSql("
            DELETE FROM `menu` WHERE label = 'SPS Eligibility'
        ");
    }
}
