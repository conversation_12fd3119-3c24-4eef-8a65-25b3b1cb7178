framework:
    messenger:
        failure_transport: failed

        transports:
            # Main async queue (e.g., using doctrine transport or Redis/SQS)
            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                options:
                    use_notify: true
                    check_delayed_interval: 60000
                retry_strategy:
                    max_retries: 3
                    multiplier: 2

            # Queue to store failed messages
            failed: 'doctrine://default?queue_name=failed'

            # Optional sync transport
            # sync: 'sync://'

        routing:
            # Built-in Symfony messages
            Symfony\Component\Mailer\Messenger\SendEmailMessage: async
            Symfony\Component\Notifier\Message\ChatMessage: async
            Symfony\Component\Notifier\Message\SmsMessage: async

            # Your custom message routed to async transport
            App\Message\PublishWidgetMessage: async
