<?php

namespace App\Document;

/**
 * SPS Eligibility Model Document (Table 2)
 *
 * Represents eligibility rules for SPS (Smart Phone Services) based on vehicle models
 * and model years. This document defines which vehicle models are eligible for
 * specific SPS features based on their model year.
 *
 * This document is stored in MongoDB Atlas and represents the second table
 * of the SPS Eligibility system.
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
class SpsEligibilityModel
{
    /**
     * MongoDB document ID
     */
    private ?string $id = null;

    /**
     * Array of vehicle model names (e.g., fiat500, spsGeneric, model3)
     *
     * @var string[]
     */
    private array $models = [];

    /**
     * Text description of eligibility rules
     */
    private ?string $eligibilityRules = null;

    /**
     * Minimum model year for eligibility (e.g., 2020)
     */
    private ?int $modelYearFrom = null;

    /**
     * Type/category of eligibility rule (e.g., "fiat500", "spsGeneric", "Premium")
     */
    private ?string $type = null;

    /**
     * Whether eligibility disclaimer is required for this rule
     */
    private bool $eligibilityDisclaimer = false;

    /**
     * Document creation timestamp
     */
    private ?\DateTime $createdAt = null;

    /**
     * Document last update timestamp
     */
    private ?\DateTime $updatedAt = null;

    /**
     * Initialize document with current timestamps
     */
    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get MongoDB document ID
     *
     * @return string|null Document ID or null if not persisted
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Set MongoDB document ID
     *
     * @param string|null $id Document ID
     * @return self
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get array of vehicle model names
     *
     * @return string[] Array of vehicle model names
     */
    public function getModels(): array
    {
        return $this->models;
    }

    /**
     * Set array of vehicle model names
     *
     * @param string[] $models Array of vehicle model names
     * @return self
     */
    public function setModels(array $models): self
    {
        $this->models = $models;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get models as comma-separated string for form display
     *
     * @return string Comma-separated model names
     */
    public function getModelsString(): string
    {
        return implode(', ', $this->models);
    }

    /**
     * Set models from comma-separated string
     *
     * @param string $modelsString Comma-separated model names
     * @return self
     */
    public function setModelsFromString(string $modelsString): self
    {
        $models = array_map('trim', explode(',', $modelsString));
        $models = array_filter($models); // Remove empty values
        $this->setModels($models);
        return $this;
    }

    /**
     * Set models from comma-separated string (alias for form compatibility)
     *
     * @param string $modelsString Comma-separated model names
     * @return self
     */
    public function setModelsString(string $modelsString): self
    {
        return $this->setModelsFromString($modelsString);
    }

    /**
     * Get eligibility rules description
     *
     * @return string|null Text description of eligibility rules
     */
    public function getEligibilityRules(): ?string
    {
        return $this->eligibilityRules;
    }

    /**
     * Set eligibility rules description
     *
     * @param string|null $eligibilityRules Text description of eligibility rules
     * @return self
     */
    public function setEligibilityRules(?string $eligibilityRules): self
    {
        $this->eligibilityRules = $eligibilityRules;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getModelYearFrom(): ?int
    {
        return $this->modelYearFrom;
    }

    public function setModelYearFrom(?int $modelYearFrom): self
    {
        $this->modelYearFrom = $modelYearFrom;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function isEligibilityDisclaimer(): bool
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(bool $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Convert to array for MongoDB storage
     */
    public function toArray(): array
    {
        return [
            'models' => $this->models,
            'eligibilityRules' => $this->eligibilityRules,
            'modelYearFrom' => $this->modelYearFrom,
            'type' => $this->type,
            'eligibilityDisclaimer' => $this->eligibilityDisclaimer,
            'createdAt' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Create from MongoDB array
     */
    public static function fromArray(array $data): self
    {
        $document = new self();
        
        if (isset($data['_id'])) {
            $document->setId($data['_id']['$oid'] ?? $data['_id']);
        }
        
        $document->setModels($data['models'] ?? []);
        $document->setEligibilityRules($data['eligibilityRules'] ?? null);
        $document->setModelYearFrom($data['modelYearFrom'] ?? null);
        $document->setType($data['type'] ?? null);
        $document->setEligibilityDisclaimer($data['eligibilityDisclaimer'] ?? false);
        
        if (isset($data['createdAt'])) {
            $document->setCreatedAt(new \DateTime($data['createdAt']));
        }
        
        if (isset($data['updatedAt'])) {
            $document->setUpdatedAt(new \DateTime($data['updatedAt']));
        }
        
        return $document;
    }
}
