<?php

namespace App\Document;

/**
 * SPS Eligibility Model Document (Table 2)
 * Stores eligibility rules based on vehicle models and model years
 */
class SpsEligibilityModel
{
    private ?string $id = null;
    
    /**
     * Array of vehicle model names
     */
    private array $models = [];
    
    /**
     * Minimum model year for eligibility (integer)
     */
    private ?int $modelYearFrom = null;
    
    /**
     * Type/category of eligibility rule
     */
    private ?string $type = null;
    
    /**
     * Whether eligibility disclaimer is required
     */
    private bool $eligibilityDisclaimer = false;
    
    /**
     * Creation timestamp
     */
    private ?\DateTime $createdAt = null;
    
    /**
     * Last update timestamp
     */
    private ?\DateTime $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getModels(): array
    {
        return $this->models;
    }

    public function setModels(array $models): self
    {
        $this->models = $models;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get models as comma-separated string for form display
     */
    public function getModelsString(): string
    {
        return implode(', ', $this->models);
    }

    /**
     * Set models from comma-separated string
     */
    public function setModelsFromString(string $modelsString): self
    {
        $models = array_map('trim', explode(',', $modelsString));
        $models = array_filter($models); // Remove empty values
        $this->setModels($models);
        return $this;
    }

    public function getModelYearFrom(): ?int
    {
        return $this->modelYearFrom;
    }

    public function setModelYearFrom(?int $modelYearFrom): self
    {
        $this->modelYearFrom = $modelYearFrom;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function isEligibilityDisclaimer(): bool
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(bool $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Convert to array for MongoDB storage
     */
    public function toArray(): array
    {
        return [
            'models' => $this->models,
            'modelYearFrom' => $this->modelYearFrom,
            'type' => $this->type,
            'eligibilityDisclaimer' => $this->eligibilityDisclaimer,
            'createdAt' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Create from MongoDB array
     */
    public static function fromArray(array $data): self
    {
        $document = new self();
        
        if (isset($data['_id'])) {
            $document->setId($data['_id']['$oid'] ?? $data['_id']);
        }
        
        $document->setModels($data['models'] ?? []);
        $document->setModelYearFrom($data['modelYearFrom'] ?? null);
        $document->setType($data['type'] ?? null);
        $document->setEligibilityDisclaimer($data['eligibilityDisclaimer'] ?? false);
        
        if (isset($data['createdAt'])) {
            $document->setCreatedAt(new \DateTime($data['createdAt']));
        }
        
        if (isset($data['updatedAt'])) {
            $document->setUpdatedAt(new \DateTime($data['updatedAt']));
        }
        
        return $document;
    }
}
