<?php

namespace App\Document;

/**
 * SPS Eligibility LCDV Document (Table 1)
 *
 * Represents eligibility rules for SPS (Smart Phone Services) based on LCDV codes.
 * LCDV codes are 4-digit vehicle identification codes used to determine
 * which vehicles are eligible for specific SPS features.
 *
 * This document is stored in MongoDB Atlas and represents the first table
 * of the SPS Eligibility system.
 *
 * <AUTHOR> BO Development Team
 * @since 2024-06-27
 */
class SpsEligibilityLcdv
{
    /**
     * MongoDB document ID
     */
    private ?string $id = null;

    /**
     * Array of LCDV codes (4-digit vehicle identification codes)
     *
     * @var string[]
     */
    private array $lcdvCodes = [];

    /**
     * Text description of eligibility rules
     */
    private ?string $eligibilityRules = null;

    /**
     * Type/category of eligibility rule (e.g., "Premium", "Standard")
     */
    private ?string $type = null;

    /**
     * Whether eligibility disclaimer is required for this rule
     */
    private bool $eligibilityDisclaimer = false;

    /**
     * Document creation timestamp
     */
    private ?\DateTime $createdAt = null;

    /**
     * Document last update timestamp
     */
    private ?\DateTime $updatedAt = null;

    /**
     * Initialize document with current timestamps
     */
    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get MongoDB document ID
     *
     * @return string|null Document ID or null if not persisted
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Set MongoDB document ID
     *
     * @param string|null $id Document ID
     * @return self
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get array of LCDV codes
     *
     * @return string[] Array of 4-digit LCDV codes
     */
    public function getLcdvCodes(): array
    {
        return $this->lcdvCodes;
    }

    /**
     * Set array of LCDV codes
     *
     * @param string[] $lcdvCodes Array of 4-digit LCDV codes
     * @return self
     */
    public function setLcdvCodes(array $lcdvCodes): self
    {
        $this->lcdvCodes = $lcdvCodes;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get LCDV codes as comma-separated string for form display
     *
     * @return string Comma-separated LCDV codes
     */
    public function getLcdvCodesString(): string
    {
        return implode(', ', $this->lcdvCodes);
    }

    /**
     * Set LCDV codes from comma-separated string
     *
     * @param string $lcdvCodesString Comma-separated LCDV codes
     * @return self
     */
    public function setLcdvCodesString(string $lcdvCodesString): self
    {
        $codes = array_map('trim', explode(',', $lcdvCodesString));
        $codes = array_filter($codes); // Remove empty values
        $this->setLcdvCodes($codes);
        return $this;
    }

    /**
     * Set LCDV codes from comma-separated string (alias for backward compatibility)
     *
     * @param string $lcdvCodesString Comma-separated LCDV codes
     * @return self
     */
    public function setLcdvCodesFromString(string $lcdvCodesString): self
    {
        return $this->setLcdvCodesString($lcdvCodesString);
    }

    public function getEligibilityRules(): ?string
    {
        return $this->eligibilityRules;
    }

    public function setEligibilityRules(?string $eligibilityRules): self
    {
        $this->eligibilityRules = $eligibilityRules;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function isEligibilityDisclaimer(): bool
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(bool $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Convert to array for MongoDB storage
     */
    public function toArray(): array
    {
        return [
            'lcdvCodes' => $this->lcdvCodes,
            'eligibilityRules' => $this->eligibilityRules,
            'type' => $this->type,
            'eligibilityDisclaimer' => $this->eligibilityDisclaimer,
            'createdAt' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Create from MongoDB array
     */
    public static function fromArray(array $data): self
    {
        $document = new self();
        
        if (isset($data['_id'])) {
            $document->setId($data['_id']['$oid'] ?? $data['_id']);
        }
        
        $document->setLcdvCodes($data['lcdvCodes'] ?? []);
        $document->setEligibilityRules($data['eligibilityRules'] ?? null);
        $document->setType($data['type'] ?? null);
        $document->setEligibilityDisclaimer($data['eligibilityDisclaimer'] ?? false);
        
        if (isset($data['createdAt'])) {
            $document->setCreatedAt(new \DateTime($data['createdAt']));
        }
        
        if (isset($data['updatedAt'])) {
            $document->setUpdatedAt(new \DateTime($data['updatedAt']));
        }
        
        return $document;
    }
}
