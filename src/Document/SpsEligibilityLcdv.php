<?php

namespace App\Document;

/**
 * SPS Eligibility LCDV Document (Table 1)
 *
 * Represents eligibility rules for SPS (Smart Phone Services) based on LCDV codes.
 * LCDV codes are vehicle identification codes (up to 20 characters) used to determine
 * which vehicles are eligible for specific SPS features.
 *
 * This document is stored in MongoDB Atlas and represents the first table
 * of the SPS Eligibility system.
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
class SpsEligibilityLcdv
{
    /**
     * MongoDB document ID
     */
    private ?string $id = null;

    /**
     * Scope of eligibility (LCDV or MODEL)
     */
    private string $scope = 'LCDV';

    /**
     * Array of codes (LCDV codes for LCDV scope, model codes for MODEL scope)
     *
     * @var string[]
     */
    private array $codes = [];

    /**
     * Text description of eligibility rule
     */
    private ?string $eligibilityRule = null;

    /**
     * Type/category of eligibility rule (e.g., "Premium", "Standard")
     */
    private ?string $type = null;

    /**
     * Eligibility disclaimer text
     */
    private ?string $eligibilityDisclaimer = null;

    /**
     * Document creation timestamp
     */
    private ?\DateTime $createdAt = null;

    /**
     * Document last update timestamp
     */
    private ?\DateTime $updatedAt = null;

    /**
     * Initialize document with current timestamps
     */
    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get MongoDB document ID
     *
     * @return string|null Document ID or null if not persisted
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Set MongoDB document ID
     *
     * @param string|null $id Document ID
     * @return self
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get scope
     */
    public function getScope(): string
    {
        return $this->scope;
    }

    /**
     * Set scope
     */
    public function setScope(string $scope): self
    {
        $this->scope = $scope;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get array of codes
     *
     * @return string[]
     */
    public function getCodes(): array
    {
        return $this->codes;
    }

    /**
     * Set array of codes
     *
     * @param string[] $codes
     * @return self
     */
    public function setCodes(array $codes): self
    {
        $this->codes = $codes;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get array of LCDV codes (legacy method)
     *
     * @return string[] Array of 4-digit LCDV codes
     */
    public function getLcdvCodes(): array
    {
        return $this->codes;
    }

    /**
     * Set array of LCDV codes
     *
     * @param string[] $lcdvCodes Array of LCDV codes (up to 20 characters each)
     * @return self
     */
    public function setLcdvCodes(array $lcdvCodes): self
    {
        $this->codes = $lcdvCodes;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get LCDV codes as comma-separated string for form display
     *
     * @return string Comma-separated LCDV codes
     */
    public function getLcdvCodesString(): string
    {
        return implode(', ', $this->codes);
    }

    /**
     * Set LCDV codes from comma-separated string
     *
     * @param string $lcdvCodesString Comma-separated LCDV codes
     * @return self
     */
    public function setLcdvCodesString(string $lcdvCodesString): self
    {
        $codes = array_map('trim', explode(',', $lcdvCodesString));
        $codes = array_filter($codes); // Remove empty values
        $this->setLcdvCodes($codes);
        return $this;
    }

    /**
     * Set LCDV codes from comma-separated string (alias for backward compatibility)
     *
     * @param string $lcdvCodesString Comma-separated LCDV codes
     * @return self
     */
    public function setLcdvCodesFromString(string $lcdvCodesString): self
    {
        return $this->setLcdvCodesString($lcdvCodesString);
    }

    public function getEligibilityRules(): ?string
    {
        return $this->eligibilityRule;
    }

    public function setEligibilityRules(?string $eligibilityRules): self
    {
        $this->eligibilityRule = $eligibilityRules;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    // New methods for the updated field names
    public function getEligibilityRule(): ?string
    {
        return $this->eligibilityRule;
    }

    public function setEligibilityRule(?string $eligibilityRule): self
    {
        $this->eligibilityRule = $eligibilityRule;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getEligibilityDisclaimer(): ?string
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(?string $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    // Legacy method for backward compatibility
    public function isEligibilityDisclaimer(): bool
    {
        return !empty($this->eligibilityDisclaimer);
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Convert to array for MongoDB storage
     */
    public function toArray(): array
    {
        return [
            'scope' => $this->scope,
            'codes' => $this->codes,
            'eligibilityRule' => $this->eligibilityRule,
            'type' => $this->type,
            'eligibilityDisclaimer' => $this->eligibilityDisclaimer,
            'createdAt' => $this->createdAt?->getTimestamp() * 1000, // MongoDB timestamp format
            'updatedAt' => $this->updatedAt?->getTimestamp() * 1000,
        ];
    }

    /**
     * Create from MongoDB array
     */
    public static function fromArray(array $data): self
    {
        $document = new self();

        if (isset($data['_id'])) {
            $document->setId($data['_id']['$oid'] ?? $data['_id']);
        }

        // Handle new structure
        $document->setScope($data['scope'] ?? 'LCDV');
        $document->setCodes($data['codes'] ?? []);
        $document->setEligibilityRule($data['eligibilityRule'] ?? null);
        $document->setType($data['type'] ?? null);
        $document->setEligibilityDisclaimer($data['eligibilityDisclaimer'] ?? null);

        // Handle legacy structure for backward compatibility
        if (isset($data['lcdvCodes'])) {
            $document->setCodes($data['lcdvCodes']);
        }
        if (isset($data['eligibilityRules'])) {
            $document->setEligibilityRule($data['eligibilityRules']);
        }

        // Handle timestamps (both string and numeric formats)
        if (isset($data['createdAt'])) {
            if (is_numeric($data['createdAt'])) {
                $document->setCreatedAt(new \DateTime('@' . intval($data['createdAt'] / 1000)));
            } else {
                $document->setCreatedAt(new \DateTime($data['createdAt']));
            }
        }

        if (isset($data['updatedAt'])) {
            if (is_numeric($data['updatedAt'])) {
                $document->setUpdatedAt(new \DateTime('@' . intval($data['updatedAt'] / 1000)));
            } else {
                $document->setUpdatedAt(new \DateTime($data['updatedAt']));
            }
        }

        return $document;
    }
}
