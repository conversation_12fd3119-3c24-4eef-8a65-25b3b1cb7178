<?php

namespace App\Command;

use App\Service\MongoAtlasQueryService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-mongo',
    description: 'Test MongoDB Atlas connection and show collections',
)]
class TestMongoCommand extends Command
{
    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('MongoDB Atlas Configuration Test');

        $io->section('Configuration');
        $io->text([
            'Database: SpaceDb',
            'DataSource: mongodb-atlas',
            'Base URL: https://eu-west-1.aws.data.mongodb-api.com',
            'App: spacemiddev-oykmhwr',
        ]);

        $io->section('Testing Collections');

        // Test SPS Eligibility LCDV collection
        $io->text('Checking spsEligibilityLcdv collection...');
        try {
            $response = $this->mongoService->find('spsEligibilityLcdv', []);
            $io->text("Status: " . $response->getCode());

            if ($response->getCode() == 200) {
                $data = $response->getData();
                $count = count($data['documents'] ?? []);
                $io->success("✅ spsEligibilityLcdv collection accessible - {$count} documents found");

                if ($count > 0) {
                    $io->text("Sample document:");
                    $io->text(json_encode($data['documents'][0], JSON_PRETTY_PRINT));
                }
            } else {
                $io->error("❌ Failed to access spsEligibilityLcdv collection");
                $io->text("Response: " . json_encode($response->getData()));
            }
        } catch (\Exception $e) {
            $io->error("❌ Error accessing spsEligibilityLcdv: " . $e->getMessage());
        }

        // Test SPS Eligibility Model collection
        $io->text('Checking spsEligibilityModel collection...');
        try {
            $response = $this->mongoService->find('spsEligibilityModel', []);
            $io->text("Status: " . $response->getCode());

            if ($response->getCode() == 200) {
                $data = $response->getData();
                $count = count($data['documents'] ?? []);
                $io->success("✅ spsEligibilityModel collection accessible - {$count} documents found");

                if ($count > 0) {
                    $io->text("Sample document:");
                    $io->text(json_encode($data['documents'][0], JSON_PRETTY_PRINT));
                }
            } else {
                $io->error("❌ Failed to access spsEligibilityModel collection");
                $io->text("Response: " . json_encode($response->getData()));
            }
        } catch (\Exception $e) {
            $io->error("❌ Error accessing spsEligibilityModel: " . $e->getMessage());
        }

        // Test Vehicle Model collection (this should work)
        $io->text('Checking vehicleLabel collection...');
        try {
            $response = $this->mongoService->find('vehicleLabel', []);
            $io->text("Status: " . $response->getCode());

            if ($response->getCode() == 200) {
                $data = json_decode($response->getData(), true);
                $count = count($data['documents'] ?? []);
                $io->success("✅ vehicleLabel collection accessible - {$count} documents found");

                if ($count > 0) {
                    $io->text("Sample document:");
                    $io->text(json_encode($data['documents'][0], JSON_PRETTY_PRINT));
                }

                // Test with a non-empty filter to see if that works
                $io->text("Testing vehicleLabel with non-empty filter...");
                $filterResponse = $this->mongoService->find('vehicleLabel', ['brand' => 'TEST']);
                $io->text("Filter Status: " . $filterResponse->getCode());

            } else {
                $io->error("❌ Failed to access vehicleLabel collection");
                $io->text("Response: " . json_encode($response->getData()));
            }
        } catch (\Exception $e) {
            $io->error("❌ Error accessing vehicleLabel: " . $e->getMessage());
        }

        // Test insertOne to see what status code it returns
        $io->section('Testing Insert Operation');

        // First test insert into existing collection (vehicleLabel)
        $io->text('Testing insert into existing vehicleLabel collection...');
        try {
            $testVehicleDocument = [
                'lcdv' => ['TEST'],
                'label' => 'Test Vehicle',
                'brand' => 'TEST_BRAND',
                'visuelSettings' => [
                    'view' => '01',
                    'width' => '400',
                    'height' => '400',
                    'defaultVisuel' => 'https://test.com',
                    'type' => 'V3D'
                ]
            ];

            $response = $this->mongoService->insertOne('vehicleLabel', $testVehicleDocument);
            $io->text("VehicleLabel Insert Status Code: " . $response->getCode());

            if ($response->getCode() == 201) {
                $io->success("✅ VehicleLabel insert returned 201 (Created)");
            } elseif ($response->getCode() == 200) {
                $io->warning("⚠️ VehicleLabel insert returned 200 (OK)");
            } else {
                $io->error("❌ VehicleLabel insert failed with code: " . $response->getCode());
                $io->text("Response: " . json_encode($response->getData()));
            }

        } catch (\Exception $e) {
            $io->error("❌ Exception during vehicleLabel insert test: " . $e->getMessage());
        }

        // Now test insert into SPS collection (this will create the collection if it doesn't exist)
        $io->text('Testing insert into spsEligibilityLcdv collection...');
        try {
            $testDocument = [
                'lcdvCodes' => ['TEST'],
                'eligibilityRules' => 'Test rule for checking insert response code',
                'type' => 'test',
                'eligibilityDisclaimer' => false,
                'createdAt' => (new \DateTime())->format('Y-m-d H:i:s'),
                'updatedAt' => (new \DateTime())->format('Y-m-d H:i:s'),
            ];

            $response = $this->mongoService->insertOne('spsEligibilityLcdv', $testDocument);
            $io->text("SPS Insert Status Code: " . $response->getCode());
            $io->text("SPS Insert Response: " . json_encode($response->getData()));

            if ($response->getCode() == 201) {
                $io->success("✅ SPS insert returned 201 (Created) - Controller expects this");
            } elseif ($response->getCode() == 200) {
                $io->warning("⚠️ SPS insert returned 200 (OK) - Controller expects 201, this is the issue!");
            } else {
                $io->error("❌ SPS insert failed with code: " . $response->getCode());
                $io->text("💡 This likely means the collection doesn't exist or Data API rules are not configured");
            }

        } catch (\Exception $e) {
            $io->error("❌ Exception during SPS insert test: " . $e->getMessage());
        }

        // Test spsEligibilityModel collection as well
        $io->text('Testing insert into spsEligibilityModel collection...');
        try {
            $testModelDocument = [
                'modelYear' => 2024,
                'eligibilityRules' => 'Test model rule',
                'type' => 'test',
                'eligibilityDisclaimer' => false,
                'createdAt' => (new \DateTime())->format('Y-m-d H:i:s'),
                'updatedAt' => (new \DateTime())->format('Y-m-d H:i:s'),
            ];

            $response = $this->mongoService->insertOne('spsEligibilityModel', $testModelDocument);
            $io->text("SPS Model Insert Status Code: " . $response->getCode());

            if ($response->getCode() == 201) {
                $io->success("✅ SPS Model insert returned 201 (Created)");
            } elseif ($response->getCode() == 200) {
                $io->warning("⚠️ SPS Model insert returned 200 (OK)");
            } else {
                $io->error("❌ SPS Model insert failed with code: " . $response->getCode());
                $io->text("Response: " . json_encode($response->getData()));
            }

        } catch (\Exception $e) {
            $io->error("❌ Exception during SPS Model insert test: " . $e->getMessage());
        }

        return Command::SUCCESS;
    }
}
