<?php

namespace App\Command;

use App\Service\MongoAtlasQueryService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-mongo',
    description: 'Test MongoDB Atlas connection and show collections',
)]
class TestMongoCommand extends Command
{
    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('MongoDB Atlas Configuration Test');

        $io->section('Configuration');
        $io->text([
            'Database: SpaceDb',
            'DataSource: mongodb-atlas',
            'Base URL: https://eu-west-1.aws.data.mongodb-api.com',
            'App: spacemiddev-oykmhwr',
        ]);

        $io->section('Testing Collections');

        // Test SPS Eligibility LCDV collection
        $io->text('Checking spsEligibilityLcdv collection...');
        try {
            $response = $this->mongoService->find('spsEligibilityLcdv', []);
            $io->text("Status: " . $response->getCode());

            if ($response->getCode() == 200) {
                $data = $response->getData();
                $count = count($data['documents'] ?? []);
                $io->success("✅ spsEligibilityLcdv collection accessible - {$count} documents found");

                if ($count > 0) {
                    $io->text("Sample document:");
                    $io->text(json_encode($data['documents'][0], JSON_PRETTY_PRINT));
                }
            } else {
                $io->error("❌ Failed to access spsEligibilityLcdv collection");
                $io->text("Response: " . json_encode($response->getData()));
            }
        } catch (\Exception $e) {
            $io->error("❌ Error accessing spsEligibilityLcdv: " . $e->getMessage());
        }

        // Test SPS Eligibility Model collection
        $io->text('Checking spsEligibilityModel collection...');
        try {
            $response = $this->mongoService->find('spsEligibilityModel', []);
            $io->text("Status: " . $response->getCode());

            if ($response->getCode() == 200) {
                $data = $response->getData();
                $count = count($data['documents'] ?? []);
                $io->success("✅ spsEligibilityModel collection accessible - {$count} documents found");

                if ($count > 0) {
                    $io->text("Sample document:");
                    $io->text(json_encode($data['documents'][0], JSON_PRETTY_PRINT));
                }
            } else {
                $io->error("❌ Failed to access spsEligibilityModel collection");
                $io->text("Response: " . json_encode($response->getData()));
            }
        } catch (\Exception $e) {
            $io->error("❌ Error accessing spsEligibilityModel: " . $e->getMessage());
        }

        // Test Vehicle Model collection
        $io->text('Checking vehicleLabel collection...');
        try {
            $response = $this->mongoService->find('vehicleLabel', []);
            $io->text("Status: " . $response->getCode());

            if ($response->getCode() == 200) {
                $data = $response->getData();
                $count = count($data['documents'] ?? []);
                $io->success("✅ vehicleLabel collection accessible - {$count} documents found");

                if ($count > 0) {
                    $io->text("Sample document:");
                    $io->text(json_encode($data['documents'][0], JSON_PRETTY_PRINT));
                }
            } else {
                $io->error("❌ Failed to access vehicleLabel collection");
                $io->text("Response: " . json_encode($response->getData()));
            }
        } catch (\Exception $e) {
            $io->error("❌ Error accessing vehicleLabel: " . $e->getMessage());
        }

        return Command::SUCCESS;
    }
}
