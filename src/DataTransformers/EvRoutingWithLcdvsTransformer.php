<?php

declare(strict_types=1);

namespace App\DataTransformers;

use App\Entity\LcdvEvRouting;
use App\Entity\EvRouting;
use Doctrine\Common\Collections\Collection;

class EvRoutingWithLcdvsTransformer extends AbstractTransformer
{
    /**
     * Transform the entity.
     *
     * @param EvRouting $evRouting
     *
     * @return array
     */
    public function transform(EvRouting $evRouting): array
    {
        return [
            'Enabled'                                   => $evRouting->isEnabled(),
            'Label'                                     => $evRouting->getLabel(),
            'LCDVs'                                     => static::extractLcdvs($evRouting->getLcdvs()),
            'constantSpeedConsumptionInkWhPerHundredkm' => $evRouting->getConstantSpeedConsumptionInkWhPerHundredkm(),
            'engineType'                                => $evRouting->getEngineType(),
            'maxChargeInkWh'                            => $evRouting->getMaxChargeInkWh(),
            'vehicleMaxSpeed'                           => $evRouting->getVehicleMaxSpeed(),
            'vehicleWeight'                             => $evRouting->getVehicleWeight(),
            'vehicleAxleWeight'                         => $evRouting->getVehicleAxleWeight(),
            'vehicleLength'                             => $evRouting->getVehicleLength(),
            'vehicleWidth'                              => $evRouting->getVehicleWidth(),
            'vehicleHeight'                             => $evRouting->getVehicleHeight(),
            'accelerationEfficiency'                    => $evRouting->getAccelerationEfficiency(),
            'decelerationEfficiency'                    => $evRouting->getDecelerationEfficiency(),
            'uphillEfficiency'                          => $evRouting->getUphillEfficiency(),
            'downhillEfficiency'                        => $evRouting->getDownhillEfficiency(),
            'chargingCurveArray'                        => $evRouting->getChargingCurveArray()
        ];
    }

    public function collection(array $collection): array
    {
        return array_map(function(EvRouting $evRouting) {
            return $this->transform($evRouting);
        }, $collection);
    }

    protected static function extractLcdvs(Collection $collection): array
    {
        $lcdvs = [];

        foreach ($collection as $item) {
            /** @var  \App\Entity\LcdvEvRouting  $item */
            $lcdvs[] = $item->getLcdv();
        }

        return $lcdvs;
    }
}
