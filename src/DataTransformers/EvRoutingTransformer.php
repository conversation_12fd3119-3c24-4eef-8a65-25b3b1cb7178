<?php

declare(strict_types=1);

namespace App\DataTransformers;

use App\Entity\EvRouting;

class EvRoutingTransformer extends AbstractTransformer
{
    /**
     * Transform the entity.
     *
     * @param EvRouting $evRouting
     *
     * @return array
     */
    public function transform(EvRouting $evRouting): array
    {
        return [
            'enabled'      => $evRouting->isEnabled(),
            'label'      => $evRouting->getLabel(),
            'constantSpeedConsumptionInkWhPerHundredkm' => $evRouting->getConstantSpeedConsumptionInkWhPerHundredkm(),
            'engineType' => $evRouting->getEngineType(),
            'maxChargeInkWh' => $evRouting->getMaxChargeInkWh(),
            'vehicleMaxSpeed' => $evRouting->getVehicleMaxSpeed(),
            'vehicleWeight' => $evRouting->getVehicleWeight(),
            'vehicleAxleWeight' => $evRouting->getVehicleAxleWeight(),
            'vehicleLength' => $evRouting->getVehicleLength(),
            'vehicleWidth' => $evRouting->getVehicleWidth(),
            'vehicleHeight' => $evRouting->getVehicleHeight(),
            'accelerationEfficiency' => $evRouting->getAccelerationEfficiency(),
            'decelerationEfficiency' => $evRouting->getDecelerationEfficiency(),
            'uphillEfficiency' => $evRouting->getUphillEfficiency(),
            'downhillEfficiency' => $evRouting->getDownhillEfficiency(),
            'chargingCurveArray' => $evRouting->getChargingCurveArray(),
        ];
    }
}
