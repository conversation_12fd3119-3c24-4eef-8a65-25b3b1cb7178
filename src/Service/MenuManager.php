<?php

namespace App\Service;

use App\Entity\Menu;
use App\Entity\Profile;
use App\Helpers\LoggerTrait;
use App\Repository\MenuRepository;
use App\Repository\ProfileMenuRepository;
use App\Repository\RoleMenuRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;

class MenuManager
{
    use LoggerTrait;
    private $request;
    private $menuRepository;
    private $profileMenuRepository;
    private $roleMenuRepository;

    public const DASHBOARD_MENU_SUFFIX = "dashboard";

    public function __construct(RequestStack $request, MenuRepository $menuRepository, ProfileMenuRepository $profileMenuRepository, RoleMenuRepository $roleMenuRepository, private RouterInterface $router)
    {
        $this->request = $request->getCurrentRequest();
        $this->menuRepository = $menuRepository;
        $this->profileMenuRepository = $profileMenuRepository;
        $this->roleMenuRepository = $roleMenuRepository;
    }

    public function getRoutePrefix(string $route): string
    {
        $route = explode('_', $route);
        $currentPrefix = "";
        if (count($route) > 2) {
            $currentPrefix = $route[0] . '_' . $route[1];
            if ($route[1] == 'menu') {
                $currentPrefix = $route[0];
            }
        } else {
            $currentPrefix = $route[0];
        }
        $this->logger->info('route prefix for {route} is {prefix}', ['route' => $route, 'prefix' => $currentPrefix]);
        return $currentPrefix;
    }

    public function getMenubyRoutePrefix(string $route): array
    {
        $menus = $this->menuRepository->getMenubyRoutePrefix($this->getRoutePrefix($route));
        $this->logger->debug('{route} refers to menus : {menus}', ['route' => $route, 'menus' => json_encode($menus)]);
        return $menus;
    }

    public function hasAccess(Profile $profile, string $route, string $method, array $parameters): bool
    {
        $menus = $this->getMenubyRoutePrefix($route);
        if (!$menus) {
            return false;
        }
        $menu = null;
        if (count($menus) > 1) {
            if ($route == "feature") {
                $label = match (true) {
                    $profile->isSuperAdministrator() => 'global_features',
                    $profile->isBrandAdministrator() => 'brand_features',
                    default => 'local_features',
                };

                $parentMenu = $this->menuRepository->findOneBy(['label' => $label]);
                $parentId = $parentMenu ? $parentMenu->getId() : null;

                if ($parentId) {
                    $menu = $this->menuRepository->findOneBy(['feature' => $parameters['id'], 'parent' => $parentId]);
                }
            } else {
                // When multiple menus found, look for exact route match first
                $menu = $this->menuRepository->findOneBy(['routeName' => $route]);
                // If no exact match, fall back to prefix match
                if (!$menu) {
                    $menu = $this->menuRepository->findOneBy(['routeName' => $this->getRoutePrefix($route)]);
                }
            }
        } else {
            $menu = $menus[0];
        }

        $profileMenu = $this->profileMenuRepository->findProfileMenuByProfileAndMenu($profile, $menu);
        $this->logger->debug('findProfileMenuByProfileAndMenu result for profile {profile} and menu {menu} is : {profileMenu}', ['menu' => $menu?->getLabel(), 'profile' => $profile?->getId(), 'profileMenu' => json_encode($profileMenu)]);
        if ($profileMenu) {
            $checkPermission = $this->checkPermission($profileMenu->getPermission(), $method);
            return $checkPermission;
        }

        $role = $profile->getRole();
        $roleMenu = $this->roleMenuRepository->findRoleMenuByRoleAndMenu($role, $menu);
        $this->logger->debug('findRoleMenuByRoleAndMenu result for profile {role} and menu {menu} is : {profileMenu}', ['menu' => $menu?->getLabel(), 'role' => $role?->getId(), 'roleMenu' => json_encode($roleMenu)]);

        if ($roleMenu) {
            $checkPermission = $this->checkPermission($roleMenu->getPermission(), $method);
            return $checkPermission;
        }
        return false;
    }

    public function checkPermission(string $permission, string $method): bool
    {
        $hasAccess = false;
        
        switch ($permission) {
            case "R":
                $hasAccess = $method == "GET";
                break;
            case "W":
                $hasAccess = true;
                break;
            case "D":
                $hasAccess = false;
                break;
        }
        $this->logger->debug("checkPermission  result for permission = $permission and method= $method is $hasAccess");
        
        return $hasAccess;
    }

    public function getRefererRoute(Profile $profile, Request $request): array
    {
        $referer = $request->headers->get('referer');
        if (is_string($referer) && $referer) {
            $refererPathInfo = Request::create($referer)->getPathInfo();
            $routeInfos = $this->router->match($refererPathInfo);
            $routeInfos['profile'] = $profile->getId();
            $refererRoute = $routeInfos['_route'] ?? '';
            $method = $request->getMethod();
            $parameters = $routeInfos;
            unset($parameters['_controller'], $parameters['_route']);

            if ($this->hasAccess($profile, $refererRoute, $method, $parameters)) {
                return ['route' => $refererRoute, 'params' => $parameters];
            }
        }

        return ['route' => 'admin', 'params' => []];
    }

    public function getAuthorizedDasbhoardMenus(?Profile $profile): array
    {
        if ($profile === null) {
            return [];
        }
        
        $dashboardMenus =  $this->menuRepository->getMenubyRouteSuffix(self::DASHBOARD_MENU_SUFFIX);
        $authorizedDashboardMenus = [];

        /**
         * @var Menu $dashboardMenu
         */
        foreach ($dashboardMenus as $dashboardMenu) {
            if ($this->hasAccess($profile, $dashboardMenu->getRouteName(), Request::METHOD_GET, [])) {
                $authorizedDashboardMenus[] = $dashboardMenu;
            }
        }
        return $authorizedDashboardMenus;
    }
}