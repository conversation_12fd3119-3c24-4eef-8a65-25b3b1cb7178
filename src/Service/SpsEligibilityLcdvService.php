<?php

namespace App\Service;

use App\Document\SpsEligibilityLcdv;
use App\Helpers\WSResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * SPS Eligibility LCDV Service
 *
 * Manages LCDV (4-digit code) based eligibility rules in MongoDB Atlas.
 * Provides CRUD operations and search functionality for SPS eligibility
 * rules based on LCDV codes.
 *
 * <AUTHOR> BO Team
 * @since 2025-06-27
 */
class SpsEligibilityLcdvService
{
    private const COLLECTION = 'spsEligibilityLcdv';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    /**
     * Find all LCDV eligibility rules
     *
     * @return SpsEligibilityLcdv[] Array of LCDV eligibility rules
     */
    public function findAll(): array
    {
        $response = $this->mongoService->find(self::COLLECTION, []);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(
            fn($doc) => SpsEligibilityLcdv::fromArray($doc),
            $documents
        );
    }

    /**
     * Find LCDV eligibility rule by ID
     *
     * @param string $id MongoDB ObjectId as string
     * @return SpsEligibilityLcdv|null The eligibility rule or null if not found
     */
    public function findById(string $id): ?SpsEligibilityLcdv
    {
        $filter = ['_id' => ['$oid' => $id]];
        $response = $this->mongoService->findOne(self::COLLECTION, $filter);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return null;
        }

        $data = json_decode($response->getData(), true);
        $document = $data['document'] ?? null;

        return $document ? SpsEligibilityLcdv::fromArray($document) : null;
    }

    /**
     * Save LCDV eligibility rule
     */
    public function save(SpsEligibilityLcdv $eligibility): WSResponse
    {
        if ($eligibility->getId()) {
            return $this->update($eligibility);
        }

        return $this->mongoService->insertOne(self::COLLECTION, $eligibility->toArray());
    }

    /**
     * Update existing LCDV eligibility rule
     */
    public function update(SpsEligibilityLcdv $eligibility): WSResponse
    {
        $filter = ['_id' => ['$oid' => $eligibility->getId()]];
        $update = ['$set' => $eligibility->toArray()];
        
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $update);
    }

    /**
     * Delete LCDV eligibility rule
     */
    public function delete(string $id): WSResponse
    {
        $filter = ['_id' => ['$oid' => $id]];
        return $this->mongoService->deleteOne(self::COLLECTION, $filter);
    }

    /**
     * Search LCDV eligibility rules
     */
    public function search(?string $lcdvCode = null, ?string $type = null, ?bool $disclaimer = null): array
    {
        $filter = [];

        if ($lcdvCode) {
            $filter['lcdvCodes'] = ['$in' => [$lcdvCode]];
        }

        if ($type) {
            $filter['type'] = $type;
        }

        if ($disclaimer !== null) {
            $filter['eligibilityDisclaimer'] = $disclaimer;
        }

        $response = $this->mongoService->find(self::COLLECTION, $filter);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return SpsEligibilityLcdv::fromArray($doc);
        }, $documents);
    }

    /**
     * Get distinct types
     */
    public function getDistinctTypes(): array
    {
        $pipeline = [
            ['$group' => ['_id' => '$type']],
            ['$match' => ['_id' => ['$ne' => null]]],
            ['$sort' => ['_id' => 1]]
        ];

        $response = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return $doc['_id'];
        }, $documents);
    }

    /**
     * Check for duplicate LCDV codes
     */
    public function findDuplicateLcdvCodes(array $lcdvCodes, ?string $excludeId = null): array
    {
        $filter = [
            'lcdvCodes' => [
                '$elemMatch' => [
                    '$in' => $lcdvCodes
                ]
            ]
        ];

        if ($excludeId) {
            $filter['_id'] = ['$ne' => ['$oid' => $excludeId]];
        }

        $response = $this->mongoService->find(self::COLLECTION, $filter);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return SpsEligibilityLcdv::fromArray($doc);
        }, $documents);
    }
}
