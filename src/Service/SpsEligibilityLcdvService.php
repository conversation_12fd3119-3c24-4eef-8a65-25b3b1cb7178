<?php

namespace App\Service;

use App\Document\SpsEligibilityLcdv;
use App\Helpers\WSResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * SPS Eligibility LCDV Service
 *
 * Manages LCDV (4-digit code) based eligibility rules in MongoDB Atlas.
 * Provides CRUD operations and search functionality for SPS eligibility
 * rules based on LCDV codes.
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
class SpsEligibilityLcdvService
{
    private const COLLECTION = 'boSPSEligibility';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    /**
     * Find all LCDV eligibility rules
     *
     * @return SpsEligibilityLcdv[] Array of LCDV eligibility rules
     */
    public function findAll(): array
    {
        // Filter by scope to get only LCDV records
        $filter = ['scope' => 'LCDV'];
        $response = $this->mongoService->find(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(
            fn($doc) => SpsEligibilityLcdv::fromArray($doc),
            $documents
        );
    }

    /**
     * Find LCDV eligibility rule by ID
     *
     * @param string $id MongoDB ObjectId as string
     * @return SpsEligibilityLcdv|null The eligibility rule or null if not found
     */
    public function findById(string $id): ?SpsEligibilityLcdv
    {
        $filter = [
            '_id' => ['$oid' => $id],
            'scope' => 'LCDV'
        ];
        $response = $this->mongoService->findOne(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return null;
        }

        $data = json_decode($response->getData(), true);
        $document = $data['document'] ?? null;

        return $document ? SpsEligibilityLcdv::fromArray($document) : null;
    }

    /**
     * Save LCDV eligibility rule (create or update)
     *
     * @param SpsEligibilityLcdv $eligibilityRule The LCDV rule to save
     * @return WSResponse MongoDB Atlas API response
     */
    public function save(SpsEligibilityLcdv $eligibilityRule): WSResponse
    {
        // Ensure scope is set to LCDV
        $eligibilityRule->setScope('LCDV');

        if ($eligibilityRule->getId()) {
            return $this->update($eligibilityRule);
        }

        return $this->mongoService->insertOne(self::COLLECTION, $eligibilityRule->toArray());
    }

    /**
     * Update existing LCDV eligibility rule
     *
     * @param SpsEligibilityLcdv $eligibilityRule The LCDV rule to update
     * @return WSResponse MongoDB Atlas API response
     */
    public function update(SpsEligibilityLcdv $eligibilityRule): WSResponse
    {
        $mongoFilter = ['_id' => ['$oid' => $eligibilityRule->getId()]];
        $updateData = ['$set' => $eligibilityRule->toArray()];

        return $this->mongoService->updateOne(self::COLLECTION, $mongoFilter, $updateData);
    }

    /**
     * Delete LCDV eligibility rule by ID
     *
     * @param string $eligibilityRuleId MongoDB ObjectId as string
     * @return WSResponse MongoDB Atlas API response
     */
    public function delete(string $eligibilityRuleId): WSResponse
    {
        $mongoFilter = ['_id' => ['$oid' => $eligibilityRuleId]];
        return $this->mongoService->deleteOne(self::COLLECTION, $mongoFilter);
    }

    /**
     * Search LCDV eligibility rules with optional filters
     *
     * @param string|null $searchLcdvCode LCDV code to search for
     * @param string|null $filterType Type filter for eligibility rules
     * @param bool|null $requiresDisclaimer Disclaimer requirement filter
     * @return SpsEligibilityLcdv[] Array of matching eligibility rules
     */
    public function search(?string $searchLcdvCode = null, ?string $filterType = null, ?bool $requiresDisclaimer = null): array
    {
        $mongoFilter = ['scope' => 'LCDV']; // Always filter by LCDV scope

        if ($searchLcdvCode) {
            $mongoFilter['codes'] = ['$in' => [$searchLcdvCode]]; // Updated field name
        }

        if ($filterType) {
            $mongoFilter['type'] = $filterType;
        }

        if ($requiresDisclaimer !== null) {
            $mongoFilter['eligibilityDisclaimer'] = $requiresDisclaimer;
        }

        $apiResponse = $this->mongoService->find(self::COLLECTION, $mongoFilter);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $documentCollection = $responseData['documents'] ?? [];

        return array_map(
            fn(array $documentData) => SpsEligibilityLcdv::fromArray($documentData),
            $documentCollection
        );
    }

    /**
     * Get distinct eligibility rule types from the collection
     *
     * @return string[] Array of unique type values sorted alphabetically
     */
    public function getDistinctTypes(): array
    {
        $aggregationPipeline = [
            ['$group' => ['_id' => '$type']],
            ['$match' => ['_id' => ['$ne' => null]]],
            ['$sort' => ['_id' => 1]]
        ];

        $apiResponse = $this->mongoService->aggregate(self::COLLECTION, $aggregationPipeline);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $aggregationResults = $responseData['documents'] ?? [];

        return array_map(
            fn(array $aggregationResult) => $aggregationResult['_id'],
            $aggregationResults
        );
    }

    /**
     * Find existing rules that contain any of the specified LCDV codes
     *
     * @param string[] $lcdvCodesToCheck Array of LCDV codes to check for duplicates
     * @param string|null $excludeRuleId Optional rule ID to exclude from search
     * @return SpsEligibilityLcdv[] Array of rules containing duplicate LCDV codes
     */
    public function findDuplicateLcdvCodes(array $lcdvCodesToCheck, ?string $excludeRuleId = null): array
    {
        $mongoFilter = [
            'lcdvCodes' => [
                '$elemMatch' => [
                    '$in' => $lcdvCodesToCheck
                ]
            ]
        ];

        if ($excludeRuleId) {
            $mongoFilter['_id'] = ['$ne' => ['$oid' => $excludeRuleId]];
        }

        $apiResponse = $this->mongoService->find(self::COLLECTION, $mongoFilter);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $documentCollection = $responseData['documents'] ?? [];

        return array_map(
            fn(array $documentData) => SpsEligibilityLcdv::fromArray($documentData),
            $documentCollection
        );
    }
}
