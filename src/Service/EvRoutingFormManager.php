<?php

namespace App\Service;

use App\Entity\{EvRouting, Language, LcdvEvRouting, PsaSite, Profile, RpoEvRouting};
use App\Repository\LcdvEvRoutingRepository;
use App\Repository\RpoEvRoutingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class EvRoutingFormManager
{
    const LCDV_INPUT = "lcdv";
    const DVQ_INPUT = "dvq";
    const B0F_INPUT = "b0f";
    const DAR_INPUT = "dar";
    const RPO_INPUT = "rpo";
    const LABEL_INPUT = "label";
    const BRAND_INPUT = "brand";
    const ENABLED_INPUT = "enabled";
    const ENGINE_TYPE_INPUT = "engineType";
    const CSCIPH_INPUT = "constantSpeedConsumptionInkWhPerHundredkm";
    const MAX_CHARGE_INPUT = "maxChargeInkWh";
    const MAX_SPEED_INPUT = "vehicleMaxSpeed";
    const WEIGHT_INPUT = "vehicleWeight";
    const AXLE_WEIGHT_INPUT = "vehicleAxleWeight";
    const LENGTH_INPUT = "vehicleLength";
    const WIDTH_INPUT = "vehicleWidth";
    const HEIGHT_INPUT = "vehicleHeight";
    const ACCELERATION_E_INPUT = "accelerationEfficiency";
    const DECELERATION_E_INPUT = "decelerationEfficiency";
    const UPHILL_E_INPUT = "uphillEfficiency";
    const DOWNHILL_E_INPUT = "downhillEfficiency";
    const CHARGING_CURVE_ARRAY_INPUT = "chargingCurveArray";


    /**
     * @var ValidatorInterface
     */
    private $validator;

    /**
     * @var EntityManagerInterface
     */
    private $manager;

    /**
     * @var LcdvEvRoutingRepository
     */
    private $repositoryLcdv;

    /**
     * @var RpoEvRoutingRepository
     */
    private $repositoryRpo;

    public function __construct(ValidatorInterface $validator, EntityManagerInterface $manager, LcdvEvRoutingRepository $repositoryLcdv, RpoEvRoutingRepository $repositoryRpo)
    {
        $this->validator = $validator;
        $this->manager = $manager;
        $this->repositoryLcdv = $repositoryLcdv;
        $this->repositoryRpo = $repositoryRpo;
    }

    /**
     * Manage create ev routing Form
     * @param FormInterface $form
     * @param string $brand
     * @param ?Profile $profile
     * @return Form
     */
    public function manageAddForm(FormInterface $form, string $brand, ?Profile $profile = null)
    {
        $data = $form->getData();
        if ($form->isValid()) {
            $entity = new EvRouting();
            $entity->setBrand($data[self::BRAND_INPUT] ?? $brand);
            $entity->setLabel(trim($data[self::LABEL_INPUT]));
            $entity->setEnabled(trim($data[self::ENABLED_INPUT]));
            $entity->setEngineType($data[self::ENGINE_TYPE_INPUT]);
            $entity->setConstantSpeedConsumptionInkWhPerHundredkm($data[self::CSCIPH_INPUT]);
            $entity->setMaxChargeInkWh($data[self::MAX_CHARGE_INPUT]);
            $entity->setVehicleMaxSpeed($data[self::MAX_SPEED_INPUT]);
            $entity->setVehicleWeight($data[self::WEIGHT_INPUT]);
            $entity->setVehicleAxleWeight($data[self::AXLE_WEIGHT_INPUT]);
            $entity->setVehicleLength($data[self::LENGTH_INPUT]);
            $entity->setVehicleWidth($data[self::WIDTH_INPUT]);
            $entity->setVehicleHeight($data[self::HEIGHT_INPUT]);
            $entity->setAccelerationEfficiency($data[self::ACCELERATION_E_INPUT]);
            $entity->setDecelerationEfficiency($data[self::DECELERATION_E_INPUT]);
            $entity->setUphillEfficiency($data[self::UPHILL_E_INPUT]);
            $entity->setDownhillEfficiency($data[self::DOWNHILL_E_INPUT]);
            $entity->setChargingCurveArray($data[self::CHARGING_CURVE_ARRAY_INPUT]);
            $entity->setDvq($data[self::DVQ_INPUT]);
            $entity->setB0f($data[self::B0F_INPUT]);
            $entity->setDar($data[self::DAR_INPUT]);

            $entity->setCreatedAt(new \DateTime());
            $entity->setLanguage($this->manager->getRepository(Language::class)->find(1));

            $validationErrors = $this->validator->validate($entity);
            if (count($validationErrors)) {
                foreach ($validationErrors as $error) {
                    $formError = new FormError($error->getMessage());
                    $form->get(self::LABEL_INPUT)->addError($formError);
                }
            } else {
                $this->manager->persist($entity);
                if (in_array($brand, ['OP', 'VX'])) {
                    list($form, $array) = $this->checkAndFormatDataOV($form);
                    $form = $this->persistData($form, $array, $entity);
                } else {
                    list($form, $lcdvs) = $this->checkAndFormatData($form);
                    $form = $this->persistData($form, $lcdvs, $entity);
                }
                if ($form->isValid()) {
                    $this->manager->flush();
                }
            }
        }
        return $form;
    }

    /**
     * @param FormInterface $form
     * @return array
     */
    private function checkAndFormatDataOV(FormInterface $form): array
    {
        $lcdvs = preg_replace('~\r[\n]?~', "\n", $form->getData()[self::LCDV_INPUT]);
        $lcdvs = array_filter(explode(PHP_EOL, $lcdvs));
        $rpos = preg_replace('~\r[\n]?~', "\n", $form->getData()[self::RPO_INPUT]);
        $rpos = array_filter(explode(PHP_EOL, $rpos));
        if (!count($lcdvs) && !count($rpos)) {
            $formError = new FormError("One of LCDV or RPO list should not be blank");
            $form->get(self::RPO_INPUT)->addError($formError);
            $form->get(self::LCDV_INPUT)->addError($formError);
        }
        return array($form, ['lcdvs' => array_unique($lcdvs), 'rpos' => array_unique($rpos)]);
    }

    /**
     * @param FormInterface $form
     * @return array
     */
    private function checkAndFormatData(FormInterface $form): array
    {
        $lcdvs = preg_replace('~\r[\n]?~', "\n", $form->getData()[self::LCDV_INPUT]);
        $lcdvs = explode(PHP_EOL, $lcdvs);
        if (!count($lcdvs)) {
            $formError = new FormError("lcdv list should not be blank");
            $form->get(self::LCDV_INPUT)->addError($formError);
        }
        return array($form, array_unique(['lcdvs' => $lcdvs]));
    }

    /**
     * @param FormInterface $form
     * @param $data
     * @param EvRouting $evRouting
     * @return FormInterface
     * @throws \Exception
     */
    private function persistData(FormInterface $form, $data, EvRouting $evRouting): FormInterface
    {
        $dataLcdv = $data['lcdvs'] ?? '';
        $dataRpo = $data['rpos'] ?? '';

        if ($dataLcdv) {
            foreach ($dataLcdv as $lcdv) {
                $lcdvEvrouting = $this->repositoryLcdv->findOneBy(['lcdv' => $lcdv, 'evRouting' => $evRouting]);
                if (!$lcdvEvrouting) {
                    $lcdvEvrouting = new LcdvEvRouting();
                    $lcdvEvrouting->setCreatedAt(new \DateTime());
                }
                $lcdvEvrouting->setLcdv(trim($lcdv));
                $lcdvEvrouting->setEvRouting($evRouting);
                $validationErrors = $this->validator->validate($lcdvEvrouting);
                if (count($validationErrors)) {
                    foreach ($validationErrors as $error) {
                        $formError = new FormError($error->getMessage());
                        $form->get($error->getPropertyPath())->addError($formError);
                    }
                    continue;
                } elseif ($form->isValid()) {
                    $this->manager->persist($lcdvEvrouting);
                    $evRouting->addLcdv($lcdvEvrouting);
                }

            }
        }
        if ($dataRpo) {
            foreach ($dataRpo as $rpo) {
                $rpoEvRouting = $this->repositoryRpo->findOneBy(['rpo' => $rpo, 'evRouting' => $evRouting]);
                if (!$rpoEvRouting) {
                    $rpoEvRouting = new RpoEvRouting();
                    $rpoEvRouting->setCreatedAt(new \DateTime());
                }
                $rpoEvRouting->setRpo(trim($rpo));
                $rpoEvRouting->setEvRouting($evRouting);
                $validationErrors = $this->validator->validate($rpoEvRouting);
                if (count($validationErrors)) {
                    foreach ($validationErrors as $error) {
                        $formError = new FormError($error->getMessage());
                        $form->get($error->getPropertyPath())->addError($formError);
                    }
                    continue;
                } elseif ($form->isValid()) {
                    $this->manager->persist($rpoEvRouting);
                    $evRouting->addRpo($rpoEvRouting);
                }
            }
        }
        return $form;
    }

    /**
     * Manage get Ev routing Form
     * @param EvRouting $evRouting
     * @return array
     */
    public function getFormData(EvRouting $evRouting)
    {
        $label = $evRouting->getLabel();
        $enabled = $evRouting->isEnabled();
        $lcdv = '';

        foreach ($evRouting->getLcdvs() as $datum) {
            $lcdv .= $datum->getLcdv() . "\r\n";
        }

        $rpo = '';

        foreach ($evRouting->getRpos() as $datum) {
            $rpo .= $datum->getRpo() . "\r\n";
        }

        $engineType = $evRouting->getEngineType();
        $csciph = $evRouting->getConstantSpeedConsumptionInkWhPerHundredkm();
        $maxCharge = $evRouting->getMaxChargeInkWh();
        $maxSpeed = $evRouting->getVehicleMaxSpeed();
        $weight = $evRouting->getVehicleWeight();
        $axleWeight = $evRouting->getVehicleAxleWeight();
        $length = $evRouting->getVehicleLength();
        $width = $evRouting->getVehicleWidth();
        $height = $evRouting->getVehicleHeight();
        $accelerationE = $evRouting->getAccelerationEfficiency();
        $decelerationE = $evRouting->getDecelerationEfficiency();
        $uphillE = $evRouting->getUphillEfficiency();
        $downhillE = $evRouting->getDownhillEfficiency();
        $charginCurveArray = $evRouting->getChargingCurveArray();
        $dvq = $evRouting->getDvq();
        $b0f = $evRouting->getB0f();
        $dar = $evRouting->getDar();

        return [
            self::LABEL_INPUT => $label,
            self::BRAND_INPUT => $evRouting->getBrand(),
            self::LCDV_INPUT => $lcdv,
            self::DVQ_INPUT => $dvq,
            self::B0F_INPUT => $b0f,
            self::DAR_INPUT => $dar,
            self::RPO_INPUT => $rpo,
            self::ENGINE_TYPE_INPUT => $engineType,
            self::CSCIPH_INPUT => $csciph,
            self::ENABLED_INPUT => $enabled,
            self::MAX_SPEED_INPUT => $maxSpeed,
            self::MAX_CHARGE_INPUT => $maxCharge,
            self::WEIGHT_INPUT => $weight,
            self::AXLE_WEIGHT_INPUT => $axleWeight,
            self::LENGTH_INPUT => $length,
            self::WIDTH_INPUT => $width,
            self::HEIGHT_INPUT => $height,
            self::ACCELERATION_E_INPUT => $accelerationE,
            self::DECELERATION_E_INPUT => $decelerationE,
            self::UPHILL_E_INPUT => $uphillE,
            self::DOWNHILL_E_INPUT => $downhillE,
            self::CHARGING_CURVE_ARRAY_INPUT => $charginCurveArray
        ];
    }

    private function manageLcdvDataForUpdate(FormInterface $form, EvRouting $evRouting, $data)
    {
        $toRemoveLcdv = [];
        $toRemoveRpo = [];
        $lcdvs = $data['lcdvs'] ?? [];
        $rpos = $data['rpos'] ?? [];
        foreach ($evRouting->getLcdvs() as $lcdvEvrouting) {
            if (!in_array($lcdvEvrouting->getLcdv(), $lcdvs)) {
                $toRemoveLcdv[] = $lcdvEvrouting->getId();
            } else {
                unset($lcdvs[array_search($lcdvEvrouting->getLcdv(), $lcdvs)]);
            }
        }
        foreach ($evRouting->getRpos() as $rpoEvRouting) {
            if (!in_array($rpoEvRouting->getRpo(), $rpos)) {
                $toRemoveRpo[] = $rpoEvRouting->getId();
            } else {
                unset($rpos[array_search($rpoEvRouting->getRpo(), $rpos)]);
            }
        }
        $form = $this->persistData($form, $data, $evRouting);
        if ($form->isValid()) {
            foreach ($toRemoveLcdv as $id) {
                $lcdv = $this->repositoryLcdv->find($id);
                if ($lcdv) {
                    $evRouting->removeLcdv($lcdv);
                    $this->manager->remove($lcdv);
                }
            }
            foreach ($toRemoveRpo as $id) {
                $rpo = $this->repositoryRpo->find($id);
                if ($rpo) {
                    $evRouting->removeRpo($rpo);
                    $this->manager->remove($rpo);
                }
            }
        }
        if (count($toRemoveLcdv) || count($toRemoveRpo) || count($data['lcdvs']) || count($data['rpos'])) {
            $evRouting->setUpdatedAt(new \DateTime());
            $this->manager->persist($evRouting);
        }
        return $form;
    }

    /**
     * Manage ev routing edit Form
     * @param FormInterface $form
     * @param EvRouting $evRouting
     * @param Profile|null $profile
     * @param string $brand
     * @return Form
     * @throws \Exception
     */
    public function manageUpdateForm(FormInterface $form, EvRouting $evRouting, ?Profile $profile = null, string $brand)
    {
        $data = $form->getData();
        $formLabel = trim($data[self::LABEL_INPUT]);
        $formBrand = $data[self::BRAND_INPUT] ?? $brand;
        $formEngineType = trim($data[self::ENGINE_TYPE_INPUT]);
        $formCsciph = trim($data[self::CSCIPH_INPUT]);
        $formMaxCharge = trim($data[self::MAX_CHARGE_INPUT]);
        $formEnabled = trim($data[self::ENABLED_INPUT]);
        $formMaxSpeed = trim($data[self::MAX_SPEED_INPUT]);
        $formWeight = trim($data[self::WEIGHT_INPUT]);
        $formAxleWeight = trim($data[self::AXLE_WEIGHT_INPUT]);
        $formLength = trim($data[self::LENGTH_INPUT]);
        $formWidth = trim($data[self::WIDTH_INPUT]);
        $formHeight = trim($data[self::HEIGHT_INPUT]);
        $formAccelerationE = trim($data[self::ACCELERATION_E_INPUT]);
        $formDecelerationE = trim($data[self::DECELERATION_E_INPUT]);
        $formUpphillE = trim($data[self::UPHILL_E_INPUT]);
        $formDownhillE = trim($data[self::DOWNHILL_E_INPUT]);
        $formDvq = trim($data[self::DVQ_INPUT]);
        $formB0f = trim($data[self::B0F_INPUT]);
        $formDar = trim($data[self::DAR_INPUT]);
        $formChargingCurveArray = trim($data[self::CHARGING_CURVE_ARRAY_INPUT]);

        $evRouting->setLabel($formLabel);
        $evRouting->setBrand($formBrand);
        $evRouting->setEngineType($formEngineType);
        $evRouting->setConstantSpeedConsumptionInkWhPerHundredkm($formCsciph);
        $evRouting->setMaxChargeInkWh($formMaxCharge);
        $evRouting->setEnabled($formEnabled);
        $evRouting->setVehicleMaxSpeed($formMaxSpeed);
        $evRouting->setVehicleWeight($formWeight);
        $evRouting->setVehicleAxleWeight($formAxleWeight);
        $evRouting->setVehicleLength($formLength);
        $evRouting->setVehicleWidth($formWidth);
        $evRouting->setVehicleHeight($formHeight);
        $evRouting->setAccelerationEfficiency($formAccelerationE);
        $evRouting->setDecelerationEfficiency($formDecelerationE);
        $evRouting->setUphillEfficiency($formUpphillE);
        $evRouting->setDownhillEfficiency($formDownhillE);
        $evRouting->setChargingCurveArray($formChargingCurveArray);
        $evRouting->setUpdatedAt(new \DateTime());
        $evRouting->setDvq($formDvq);
        $evRouting->setB0f($formB0f);
        $evRouting->setDar($formDar);

        $validationErrors = $this->validator->validate($evRouting);
        if (count($validationErrors)) {
            foreach ($validationErrors as $error) {
                $formError = new FormError($error->getMessage());
                $form->get(self::LABEL_INPUT)->addError($formError);
            }
            return $form;
        } else {
            $this->manager->persist($evRouting);
        }
        if (in_array($brand, ['OP', 'VX'])) {
            list($form, $rpos) = $this->checkAndFormatDataOV($form);
            $this->manageLcdvDataForUpdate($form, $evRouting, $rpos);
        } else {
            list($form, $lcdvs) = $this->checkAndFormatData($form);
            $this->manageLcdvDataForUpdate($form, $evRouting, $lcdvs);
        }

        if ($form->isValid()) {
            $this->manager->flush();
        }
        return $form;
    }
}
