<?php

namespace App\Service;

use App\Document\SpsEligibilityModel;
use App\Helpers\WSResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Service for managing SPS Eligibility Model rules in MongoDB Atlas
 *
 * This service handles CRUD operations for vehicle model eligibility rules
 * that determine which vehicle models are eligible for SPS (Smart Phone Services).
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
class SpsEligibilityModelService
{
    private const COLLECTION = 'boSPSEligibility';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    /**
     * Find all Model eligibility rules
     *
     * @return SpsEligibilityModel[] Array of all model eligibility rules
     */
    public function findAll(): array
    {
        // Filter by scope to get only MODEL records
        $filter = ['scope' => 'MODEL'];
        $response = $this->mongoService->find(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(fn($doc) => SpsEligibilityModel::fromArray($doc), $documents);
    }

    /**
     * Find Model eligibility rule by ID
     *
     * @param string $id MongoDB ObjectId as string
     * @return SpsEligibilityModel|null The model rule or null if not found
     */
    public function findById(string $id): ?SpsEligibilityModel
    {
        $filter = [
            '_id' => ['$oid' => $id],
            'scope' => 'MODEL'
        ];
        $response = $this->mongoService->findOne(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return null;
        }

        $data = json_decode($response->getData(), true);
        $document = $data['document'] ?? null;

        return $document ? SpsEligibilityModel::fromArray($document) : null;
    }

    /**
     * Save Model eligibility rule (create or update)
     *
     * @param SpsEligibilityModel $eligibilityRule The model rule to save
     * @return WSResponse MongoDB Atlas API response
     */
    public function save(SpsEligibilityModel $eligibilityRule): WSResponse
    {
        // Ensure scope is set to MODEL
        $eligibilityRule->setScope('MODEL');

        if ($eligibilityRule->getId()) {
            return $this->update($eligibilityRule);
        }

        return $this->mongoService->insertOne(self::COLLECTION, $eligibilityRule->toArray());
    }

    /**
     * Update existing Model eligibility rule
     *
     * @param SpsEligibilityModel $eligibilityRule The model rule to update
     * @return WSResponse MongoDB Atlas API response
     */
    public function update(SpsEligibilityModel $eligibilityRule): WSResponse
    {
        $mongoFilter = ['_id' => ['$oid' => $eligibilityRule->getId()]];
        $updateData = ['$set' => $eligibilityRule->toArray()];

        return $this->mongoService->updateOne(self::COLLECTION, $mongoFilter, $updateData);
    }

    /**
     * Delete Model eligibility rule by ID
     *
     * @param string $eligibilityRuleId MongoDB ObjectId as string
     * @return WSResponse MongoDB Atlas API response
     */
    public function delete(string $eligibilityRuleId): WSResponse
    {
        $mongoFilter = ['_id' => ['$oid' => $eligibilityRuleId]];
        return $this->mongoService->deleteOne(self::COLLECTION, $mongoFilter);
    }

    /**
     * Search Model eligibility rules with optional filters
     *
     * @param string|null $searchModel Vehicle model name to filter by
     * @param string|null $filterType Eligibility type to filter by
     * @param int|null $minimumModelYear Minimum model year to filter by
     * @param bool|null $requiresDisclaimer Disclaimer requirement to filter by
     * @return SpsEligibilityModel[] Array of matching model rules
     */
    public function search(?string $searchModel = null, ?string $filterType = null, ?int $minimumModelYear = null, ?bool $requiresDisclaimer = null): array
    {
        $mongoFilter = ['scope' => 'MODEL']; // Always filter by MODEL scope

        if ($searchModel) {
            $mongoFilter['codes'] = ['$in' => [$searchModel]]; // Updated field name
        }

        if ($filterType) {
            $mongoFilter['type'] = $filterType;
        }

        if ($minimumModelYear !== null) {
            $mongoFilter['modelYearFrom'] = ['$gte' => $minimumModelYear];
        }

        if ($requiresDisclaimer !== null) {
            $mongoFilter['eligibilityDisclaimer'] = $requiresDisclaimer;
        }

        $apiResponse = $this->mongoService->find(self::COLLECTION, $mongoFilter);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $documentCollection = $responseData['documents'] ?? [];

        return array_map(
            fn(array $documentData) => SpsEligibilityModel::fromArray($documentData),
            $documentCollection
        );
    }

    /**
     * Get distinct eligibility types from all model rules
     *
     * @return string[] Array of unique eligibility types sorted alphabetically
     */
    public function getDistinctTypes(): array
    {
        $aggregationPipeline = [
            ['$group' => ['_id' => '$type']],
            ['$match' => ['_id' => ['$ne' => null]]],
            ['$sort' => ['_id' => 1]]
        ];

        $apiResponse = $this->mongoService->aggregate(self::COLLECTION, $aggregationPipeline);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $aggregationResults = $responseData['documents'] ?? [];

        return array_map(
            fn(array $aggregationResult) => $aggregationResult['_id'],
            $aggregationResults
        );
    }

    /**
     * Get distinct vehicle models from all model rules
     *
     * @return string[] Array of unique vehicle model names sorted alphabetically
     */
    public function getDistinctModels(): array
    {
        $aggregationPipeline = [
            ['$unwind' => '$models'],
            ['$group' => ['_id' => '$models']],
            ['$sort' => ['_id' => 1]]
        ];

        $apiResponse = $this->mongoService->aggregate(self::COLLECTION, $aggregationPipeline);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $aggregationResults = $responseData['documents'] ?? [];

        return array_map(
            fn(array $aggregationResult) => $aggregationResult['_id'],
            $aggregationResults
        );
    }

    /**
     * Find existing rules that contain any of the specified vehicle models
     *
     * @param string[] $modelsToCheck Array of model names to check for duplicates
     * @param string|null $excludeRuleId Rule ID to exclude from duplicate check (for updates)
     * @return SpsEligibilityModel[] Array of rules containing duplicate models
     */
    public function findDuplicateModels(array $modelsToCheck, ?string $excludeRuleId = null): array
    {
        $mongoFilter = [
            'models' => [
                '$elemMatch' => [
                    '$in' => $modelsToCheck
                ]
            ]
        ];

        if ($excludeRuleId) {
            $mongoFilter['_id'] = ['$ne' => ['$oid' => $excludeRuleId]];
        }

        $apiResponse = $this->mongoService->find(self::COLLECTION, $mongoFilter);

        if ($apiResponse->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $responseData = json_decode($apiResponse->getData(), true);
        $documentCollection = $responseData['documents'] ?? [];

        return array_map(
            fn(array $documentData) => SpsEligibilityModel::fromArray($documentData),
            $documentCollection
        );
    }
}
