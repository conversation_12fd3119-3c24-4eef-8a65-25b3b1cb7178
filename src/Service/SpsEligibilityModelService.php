<?php

namespace App\Service;

use App\Document\SpsEligibilityModel;
use App\Helpers\WSResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Service for managing SPS Eligibility Model rules in MongoDB Atlas
 *
 * This service handles CRUD operations for vehicle model eligibility rules
 * that determine which vehicle models are eligible for SPS (Smart Phone Services).
 *
 * <AUTHOR> BO Development Team
 * @since 2024-06-27
 */
class SpsEligibilityModelService
{
    private const COLLECTION = 'spsEligibilityModel';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    /**
     * Find all Model eligibility rules
     *
     * @return SpsEligibilityModel[] Array of all model eligibility rules
     */
    public function findAll(): array
    {
        $response = $this->mongoService->find(self::COLLECTION, []);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(fn($doc) => SpsEligibilityModel::fromArray($doc), $documents);
    }

    /**
     * Find Model eligibility rule by ID
     *
     * @param string $id MongoDB ObjectId as string
     * @return SpsEligibilityModel|null The model rule or null if not found
     */
    public function findById(string $id): ?SpsEligibilityModel
    {
        $filter = ['_id' => ['$oid' => $id]];
        $response = $this->mongoService->findOne(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return null;
        }

        $data = json_decode($response->getData(), true);
        $document = $data['document'] ?? null;

        return $document ? SpsEligibilityModel::fromArray($document) : null;
    }

    /**
     * Save Model eligibility rule (create or update)
     *
     * @param SpsEligibilityModel $eligibility The model rule to save
     * @return WSResponse MongoDB Atlas API response
     */
    public function save(SpsEligibilityModel $eligibility): WSResponse
    {
        if ($eligibility->getId()) {
            return $this->update($eligibility);
        }

        return $this->mongoService->insertOne(self::COLLECTION, $eligibility->toArray());
    }

    /**
     * Update existing Model eligibility rule
     *
     * @param SpsEligibilityModel $eligibility The model rule to update
     * @return WSResponse MongoDB Atlas API response
     */
    public function update(SpsEligibilityModel $eligibility): WSResponse
    {
        $filter = ['_id' => ['$oid' => $eligibility->getId()]];
        $update = ['$set' => $eligibility->toArray()];

        return $this->mongoService->updateOne(self::COLLECTION, $filter, $update);
    }

    /**
     * Delete Model eligibility rule
     *
     * @param string $id MongoDB ObjectId as string
     * @return WSResponse MongoDB Atlas API response
     */
    public function delete(string $id): WSResponse
    {
        $filter = ['_id' => ['$oid' => $id]];
        return $this->mongoService->deleteOne(self::COLLECTION, $filter);
    }

    /**
     * Search Model eligibility rules with filters
     *
     * @param string|null $model Vehicle model name to filter by
     * @param string|null $type Eligibility type to filter by
     * @param int|null $modelYearFrom Minimum model year to filter by
     * @param bool|null $disclaimer Disclaimer requirement to filter by
     * @return SpsEligibilityModel[] Array of matching model rules
     */
    public function search(?string $model = null, ?string $type = null, ?int $modelYearFrom = null, ?bool $disclaimer = null): array
    {
        $filter = [];

        if ($model) {
            $filter['models'] = ['$in' => [$model]];
        }

        if ($type) {
            $filter['type'] = $type;
        }

        if ($modelYearFrom !== null) {
            $filter['modelYearFrom'] = ['$gte' => $modelYearFrom];
        }

        if ($disclaimer !== null) {
            $filter['eligibilityDisclaimer'] = $disclaimer;
        }

        $response = $this->mongoService->find(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(fn($doc) => SpsEligibilityModel::fromArray($doc), $documents);
    }

    /**
     * Get distinct eligibility types from all model rules
     *
     * @return string[] Array of unique eligibility types
     */
    public function getDistinctTypes(): array
    {
        $pipeline = [
            ['$group' => ['_id' => '$type']],
            ['$match' => ['_id' => ['$ne' => null]]],
            ['$sort' => ['_id' => 1]]
        ];

        $response = $this->mongoService->aggregate(self::COLLECTION, $pipeline);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(fn($doc) => $doc['_id'], $documents);
    }

    /**
     * Get distinct vehicle models from all model rules
     *
     * @return string[] Array of unique vehicle model names
     */
    public function getDistinctModels(): array
    {
        $pipeline = [
            ['$unwind' => '$models'],
            ['$group' => ['_id' => '$models']],
            ['$sort' => ['_id' => 1]]
        ];

        $response = $this->mongoService->aggregate(self::COLLECTION, $pipeline);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(fn($doc) => $doc['_id'], $documents);
    }

    /**
     * Check for duplicate vehicle models in existing rules
     *
     * @param string[] $models Array of model names to check for duplicates
     * @param string|null $excludeId Rule ID to exclude from duplicate check (for updates)
     * @return SpsEligibilityModel[] Array of rules containing duplicate models
     */
    public function findDuplicateModels(array $models, ?string $excludeId = null): array
    {
        $filter = [
            'models' => [
                '$elemMatch' => [
                    '$in' => $models
                ]
            ]
        ];

        if ($excludeId) {
            $filter['_id'] = ['$ne' => ['$oid' => $excludeId]];
        }

        $response = $this->mongoService->find(self::COLLECTION, $filter);

        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(fn($doc) => SpsEligibilityModel::fromArray($doc), $documents);
    }
}
