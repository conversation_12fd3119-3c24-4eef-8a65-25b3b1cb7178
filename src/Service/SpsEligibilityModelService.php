<?php

namespace App\Service;

use App\Document\SpsEligibilityModel;
use App\Helpers\WSResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Service for managing SPS Eligibility Model rules in MongoDB
 */
class SpsEligibilityModelService
{
    private const COLLECTION = 'spsEligibilityModel';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    /**
     * Find all Model eligibility rules
     */
    public function findAll(): array
    {
        $response = $this->mongoService->find(self::COLLECTION, []);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return SpsEligibilityModel::fromArray($doc);
        }, $documents);
    }

    /**
     * Find Model eligibility rule by ID
     */
    public function findById(string $id): ?SpsEligibilityModel
    {
        $filter = ['_id' => ['$oid' => $id]];
        $response = $this->mongoService->findOne(self::COLLECTION, $filter);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return null;
        }

        $data = json_decode($response->getData(), true);
        $document = $data['document'] ?? null;

        return $document ? SpsEligibilityModel::fromArray($document) : null;
    }

    /**
     * Save Model eligibility rule
     */
    public function save(SpsEligibilityModel $eligibility): WSResponse
    {
        if ($eligibility->getId()) {
            return $this->update($eligibility);
        }

        return $this->mongoService->insertOne(self::COLLECTION, $eligibility->toArray());
    }

    /**
     * Update existing Model eligibility rule
     */
    public function update(SpsEligibilityModel $eligibility): WSResponse
    {
        $filter = ['_id' => ['$oid' => $eligibility->getId()]];
        $update = ['$set' => $eligibility->toArray()];
        
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $update);
    }

    /**
     * Delete Model eligibility rule
     */
    public function delete(string $id): WSResponse
    {
        $filter = ['_id' => ['$oid' => $id]];
        return $this->mongoService->deleteOne(self::COLLECTION, $filter);
    }

    /**
     * Search Model eligibility rules
     */
    public function search(?string $model = null, ?string $type = null, ?int $modelYearFrom = null, ?bool $disclaimer = null): array
    {
        $filter = [];

        if ($model) {
            $filter['models'] = ['$in' => [$model]];
        }

        if ($type) {
            $filter['type'] = $type;
        }

        if ($modelYearFrom !== null) {
            $filter['modelYearFrom'] = ['$gte' => $modelYearFrom];
        }

        if ($disclaimer !== null) {
            $filter['eligibilityDisclaimer'] = $disclaimer;
        }

        $response = $this->mongoService->find(self::COLLECTION, $filter);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return SpsEligibilityModel::fromArray($doc);
        }, $documents);
    }

    /**
     * Get distinct types
     */
    public function getDistinctTypes(): array
    {
        $pipeline = [
            ['$group' => ['_id' => '$type']],
            ['$match' => ['_id' => ['$ne' => null]]],
            ['$sort' => ['_id' => 1]]
        ];

        $response = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return $doc['_id'];
        }, $documents);
    }

    /**
     * Get distinct models
     */
    public function getDistinctModels(): array
    {
        $pipeline = [
            ['$unwind' => '$models'],
            ['$group' => ['_id' => '$models']],
            ['$sort' => ['_id' => 1]]
        ];

        $response = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return $doc['_id'];
        }, $documents);
    }

    /**
     * Check for duplicate models
     */
    public function findDuplicateModels(array $models, ?string $excludeId = null): array
    {
        $filter = [
            'models' => [
                '$elemMatch' => [
                    '$in' => $models
                ]
            ]
        ];

        if ($excludeId) {
            $filter['_id'] = ['$ne' => ['$oid' => $excludeId]];
        }

        $response = $this->mongoService->find(self::COLLECTION, $filter);
        
        if ($response->getCode() !== Response::HTTP_OK) {
            return [];
        }

        $data = json_decode($response->getData(), true);
        $documents = $data['documents'] ?? [];

        return array_map(function($doc) {
            return SpsEligibilityModel::fromArray($doc);
        }, $documents);
    }
}
