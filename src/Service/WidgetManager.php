<?php

namespace App\Service;

use App\Entity\Site;
use App\Repository\{AccessLogRepository, WidgetRepository, WidgetDataRepository, WidgetFeatureRepository, WidgetFeatureAttributeRepository, LanguageRepository, BrandRepository, ChannelRepository, SiteRepository};
use App\Form\FeatureFormType;
use App\Entity\{Brand, Country, Widget, WidgetData, WidgetFeature, WidgetFeatureAttribute, Language};
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Forms;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Uid\Uuid;
use App\Helpers\LoggerTrait;
use App\Helpers\StringHelper;
use App\Factory\TranslationKeyFactory;
use App\Helpers\WSResponse;
use Webmozart\Assert\Assert;
use App\Model\FeatureDataModel;
use App\Model\FieldDataModel;
use App\Model\WidgetDataModel;
use Symfony\Component\Form\Extension\Validator\ValidatorExtension;
use Symfony\Component\Translation\Translator;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Messenger\MessageBusInterface;
use App\Message\PublishWidgetMessage;

/**
 * WidgetManager
 */
class WidgetManager
{
    use LoggerTrait;
    const MULTI_FIELDS_TYPE = 'isMultifield';
    const WIDGET = 'widget';
    const TRANSLATION_DOMAINE = 'validators';

    public function __construct(
        private EntityManagerInterface $objectManager,
        private WidgetRepository $widgetRepository,
        private WidgetFeatureRepository $widgetFeatureRepository,
        private WidgetFeatureAttributeRepository $widgetFeatureAttributeRepository,
        private LanguageRepository $languageRepository,
        private WidgetDataRepository $widgetDataRepository,
        private AWSS3Service $s3Service,
        private LabelTranslationManager $labelTranslationManager,
        private string $settingsBucket,
        private BrandRepository $brandRepository,
        private ValidatorInterface $validator,
        private TranslationKeyFactory $translationKeyFactory,
        private ChannelRepository $channelRepository,
        private SerializerInterface $serializer,
        private NormalizerInterface $normalizer,
        private AccessLogRepository $accessLogRepository,
        private SiteRepository $siteRepository,
        private MessageBusInterface $messageBus
    ) {}

    public function list(): array
    {
        return $this->widgetRepository->findAll();
    }

    public function load(Widget $widget, ?Brand $brand, ?Country $country, Collection $languages, ?string $lang = 'en', bool $brandAdmin = false): array
    {
        $featureContent = $widget->getFeaturesConfiguration();

        return $this->buildForm($widget, $featureContent, $brand, $country, $languages, $brandAdmin, $lang);
    }

    private function buildForm(Widget $widget, array $featureStructure, ?Brand $brand, ?Country $country, Collection $languages, bool $brandAdmin, ?string $lang = 'en'): array
    {
        $translator = new Translator($lang);
        //$translator->addLoader('yaml', new YamlFileLoader());
        $translator->addResource(
            'yaml',
            __DIR__ . "/../../translations/" . self::TRANSLATION_DOMAINE . ".{$lang}.yaml",
            $lang,
            self::TRANSLATION_DOMAINE
        );
        $validatorBuilder = Validation::createValidatorBuilder();
        $validatorBuilder->setTranslator($translator);
        $validatorBuilder->setTranslationDomain(self::TRANSLATION_DOMAINE);
        $validator = $validatorBuilder->getValidator();
        $formFactory = Forms::createFormFactoryBuilder()
            ->addExtension(new ValidatorExtension($validator))
            ->getFormFactory();
        $forms = [];
        $sources = FeatureFormFieldsProvider::getSources($featureStructure);
        $channelNames = $this->channelRepository->getChannelMap();

        foreach ($sources as $source) {
            if (!in_array($source, $channelNames, true)) {
                continue;
            }
            foreach ($featureStructure['features'] as $feature) {
                $featureCode = StringHelper::cleanString($feature['code']);
                $featureTitle = $feature['title'] ?? $featureCode;
                $data = $this->getFormData($widget, $featureCode, $brand, $country, $source);
                $this->updateMultiValueData($feature, $data);
                $options['languages'] = $languages;
                $options['fields'] = FeatureFormFieldsProvider::getFields($feature);
                $options['data'] = $data;
                $options['profile'] = $brandAdmin ? "BrandAdmin" : "";
                $options['attr']['typeForm'] = self::WIDGET;
                $forms[$source][$featureCode]['title'] = $featureTitle;
                $forms[$source][$featureCode]['form'] = $formFactory->createNamed($featureCode . $source, FeatureFormType::class, $data, $options);
            }
        }

        return $forms;
    }

    private function updateMultiValueData(mixed $feature, mixed &$data): void
    {
        $fields = $feature['fields'];
        foreach ($fields as $field) {
            $isMultiValue = $field['options']['attr']['multiValue'] ?? false;
            if ($isMultiValue) {
                $name = $field['name'];
                $data_url = $data[$name] ?? '';
                unset($data[$name]);
                $data[$name]['selectValues'] = json_decode($data_url);
                $data[$name]['choicesData'] = $data_url;
            }
        }
    }

    public function saveFeature(Widget $widget, string $featureCode, array $data, ?Brand $brand, ?Country $country, ?Collection $languages): WidgetFeature
    {
        $source = $data['source'] ?? '';
        $featureCode = StringHelper::cleanString($featureCode);
        $service = $this->widgetFeatureRepository->findOneBy(['brand' => $brand, 'country' => $country, 'code' => $featureCode, 'source' => $source, 'widget' => $widget]);
        $this->createWidgetDataIfNotExists($brand, $country, $source, $widget);

        if (!$service) {
            $service = new WidgetFeature();
            $service->setCode($featureCode)
                ->setWidget($widget)
                ->setLabel($data['title'])
                ->setBrand($brand)
                ->setCountry($country)
                ->setSource($source);
            $this->objectManager->persist($service);
        }

        $service->setEnabled($data['enabled'] ?? false);

        unset($data['enabled']);
        unset($data['source']);
        unset($data['code']);
        unset($data['title']);
        $this->manageAttributes($data, $service, $languages);

        $this->objectManager->flush();

        return $service;
    }

    private function manageAttributes(array $data, WidgetFeature $feature, ?Collection $languages): void
    {
        array_walk($data, function ($value, $field) use ($feature, $languages) {
            $parsedField = $this->parseField($field, $languages);
            $field = $parsedField['name'];
            if (str_ends_with($field, '_input')) return;
            $language = $this->languageRepository->findOneByCode($parsedField['language']);
            if (!$featureAttribute = $this->widgetFeatureAttributeRepository->findOneBy(['name' => $field, 'language' => $language, 'widgetFeature' => $feature])) {
                $featureAttribute = new WidgetFeatureAttribute();
                $featureAttribute->setWidgetFeature($feature);
                $featureAttribute->setName($field);
                $featureAttribute->setLanguage($language);
                $this->objectManager->persist($featureAttribute);
            }
            $featureAttribute->setType("notMultiField");
            // in the case of multi values
            if (is_array($value)) {
                if (isset($value['choicesData'])) {
                    $value = $value['choicesData'] ?? '';
                } else {
                    $multiFieldsData = [];
                    foreach ($value as $item) {
                        if ($item['type'] == self::MULTI_FIELDS_TYPE) {
                            $featureAttribute->setType(self::MULTI_FIELDS_TYPE);
                            $multiFieldsData[] = $item;
                        }
                    }
                    $value = json_encode($multiFieldsData, true);
                }
            }
            $featureAttribute->setValue($value ?? '');
        });
    }

    public function getFormData(Widget $widget, string $featureCode, ?Brand $brand, ?Country $country, ?string $source): array
    {
        $feature = $this->widgetFeatureRepository->findOneBy(['widget' => $widget, 'brand' => $brand, 'country' => $country, 'code' => $featureCode, 'source' => $source]);
        $formData = ['source' => $source, 'enabled' => $feature?->isEnabled()];
        if ($feature) {
            $formData = array_merge($formData, $this->indexObjectsByParamValue($feature->getAttributes()));
        }

        return $formData;
    }

    private function indexObjectsByParamValue($parameters): array
    {
        $data = [];
        foreach ($parameters as $parameter) {
            if ($this->isTranslatable($parameter)) {
                $data[$parameter->getName() . '__' . $parameter->getLanguage()] = $parameter->getValue();
            } else {
                $data[$parameter->getName()] = $parameter->getValue();
            }
        }

        return $data;
    }

    private function parseField(string $fieldName, ?Collection $languages): array
    {
        $field = [];
        $fieldParts = explode('__', $fieldName);
        $field['name'] = $fieldParts[0];
        $field['language'] = $fieldParts[1] ?? 'default';
        if ($languages) {
            $field['language'] = $this->isValidLanguage($field['language'], $languages) ? $field['language'] : 'default';
        }

        return $field;
    }

    private function isValidLanguage(?string $code, Collection $languages): bool
    {
        return $languages->exists(function ($key, $language) use ($code) {
            return $language->getCode() == $code;
        });
    }

    private function isTranslatable(WidgetFeatureAttribute $parameter): bool
    {
        return $parameter->getLanguage()?->getCode() !== null;
    }

    public function enable(Widget $widget, Brand $brand, ?Country $country, string $source, bool $enabled): WidgetData
    {
        $widgetData = $this->widgetDataRepository->findOneBy(['widget' => $widget, 'brand' => $brand, 'country' => $country, 'source' => $source]);
        if (!$widgetData) {
            $widgetData = new WidgetData();
            $widgetData->setWidget($widget)
                ->setSource($source)
                ->setBrand($brand)
                ->setCountry($country);
        }
        $widgetData->setEnabled($enabled);
        $this->objectManager->persist($widgetData);
        $this->objectManager->flush();

        return $widgetData;
    }

    public function saveWidget($file, Widget $widget): Widget
    {
        try {
            $uuid = Uuid::v4();
            $widget->setWguid($uuid);
            $content = file_get_contents($file);
            $content = json_decode($content, true) ?? [];
            $labels = $content['labels'] ?? [];
            $sources = $content['sources'] ?? [];
            $widget->setFeaturesConfiguration($content);
            $this->objectManager->persist($widget);
            $widget = $this->saveWidgetsLabels($labels, $sources, $widget);
            $this->objectManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('error while saveing widget ' . $e->getMessage());
        }

        return $widget;
    }
    /**
     * remove widget if no already configuration 
     * @param Widget $widget
     * @return bool
     */
    public function removeWidget(Widget $widget): bool
    {
        $translationKeys = $widget->getTranslationKeys();
        foreach ($translationKeys as $translationKey) {
            $widget->removeTranslationKey($translationKey);
        }
        if ($widget->getWidgetData()->count() == 0 && $widget->getWidgetFeatures()->count() == 0) {
            $this->objectManager->remove($widget);
            $this->objectManager->flush();

            return true;
        }

        return false;
    }

    /**
     * get widget data by Brand and/or Country
     * @param Brand $brand
     * @param Country $country
     * @return array
     */
    public function getWidgetAndConfig(?Brand $brand, ?Country $country): array
    {
        return $this->widgetRepository->findWidgetAndConfig($brand, $country);
    }

    public function getWidgetS3ResourceContent(
        string $relativeUrl
    ): string {
        $content = $this->s3Service->getObjectContent($this->settingsBucket, $relativeUrl);
        return !is_bool($content) ? $content : '';
    }

    /**
     * @param Collection|Language[] $languages
     * @return array<string, array<string, mixed>>
     */
    public function getWidgetS3ResourcesStatus(
        Widget $widget,
        ?Brand $brand,
        ?Country $country,
        Collection $languages,
        Language $referenceLanguage
    ): array {
        try {
            $resources = [];
            if ($brand === null || $country === null) {
                return $resources;
            }
            $featureContent = $widget->getFeaturesConfiguration();
            $sources = FeatureFormFieldsProvider::getSources($featureContent);
            $languages = $languages->isEmpty() ? [$referenceLanguage] : $languages;

            foreach ($sources as $source) {
                Assert::stringNotEmpty($source);
                foreach ($languages as $language) {
                    $resource = [];
                    Assert::isInstanceOf($language, Language::class);

                    // generate the file name
                    $resource['filename'] = $this->getWidgetFileName($brand, $country, $language, $source, $widget->getName() ?? '');

                    // get the file metadata, if exists on S3 bucket
                    $resourceMetaData = $this->s3Service->getObjectMetaData($this->settingsBucket, $resource['filename']);
                    if ($resourceMetaData === false) {
                        $resource['url'] = '#';
                        $resource['creationDate'] = '';
                        $resource['lastModified'] = '';
                    } else {
                        Assert::isArray($resourceMetaData);
                        $resource['url'] =  $this->settingsBucket . '/' . $resource['filename'];
                        $resource['creationDate'] = $resourceMetaData['creationDate'];
                        $resource['lastModified'] = $resourceMetaData['lastUpdateDate'];
                    }
                    $resources[$source][$language->getCode()] = $resource;
                }
            }
            return $resources;
        } catch (\Throwable $e) {
            $this->logger->error(__METHOD__ . ': error while getting widget s3 resources status ', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    public function publishWidgetData(WidgetData $widgetData, Collection $languages, Language $referenceLanguage): bool
    {
        $files = [];
        $country = $widgetData->getCountry();
        $brand = $widgetData->getBrand();
        $source = $widgetData->getSource();
        $languages = $languages->isEmpty() ? [$referenceLanguage] : $languages;

        foreach ($languages as $language) {
            $data = $this->getJsonWidgetData($widgetData, $brand, $country, $source, $language, $referenceLanguage);

            $fileName = $this->getWidgetFileName(
                $brand,
                $country,
                $language,
                $source,
                $widgetData->getWidget()->getName() ?? ''
            );
            $this->logger->debug("publishing data on file $fileName for $brand/$country/$source/$language data: $data");
            $files[$language->getCode()] = $this->s3Service->putObject($fileName, $data, $this->settingsBucket);
        }

        $this->logger->debug("publish files response:" . json_encode($files));

        return in_array(true, $files);
    }

    private function getWidgetFileName(Brand $brand, Country $country, Language $language, string $source, string $widgetName): string
    {
        $source = strtolower($source);
        $brand = strtolower($brand->getCode());
        $culture = strtolower($language->getCode()) . '-' . strtoupper($country->getCode());

        return sprintf('%s/%s/%s/%s/widget-settings.json', $source, $brand, $culture, strtolower($widgetName));
    }

    private function getJsonWidgetData(WidgetData $widgetData, Brand $brand, Country $country, string $source, Language $language, Language $referenceLanguage): string
    {
        $widget = $widgetData->getWidget();
        $widgetJson = [
            'name' => $widget->getName(),
            'description' => $widget->getDescription(),
            'guid' => $widget->getWguid(),
            'type' => $widget->getType(),
            'version' => $widget->getVersion(),
            'enabled' => $widgetData->isEnabled(),
            'brand' => $brand->getName(),
            'culture' => $language->getCode() . '-' . $country->getCode(),
            'containerType' => $widgetData->getSource(),
            'features' => $this->getFormattedFeatures($widget, $widgetData->getBrand(), $widgetData->getCountry(), $widgetData->getSource(), $language),
            'labels' => $this->labelTranslationManager->getTranslatedKeysByWidget($widgetData->getWidget(), $brand, $country, $source, $language, $referenceLanguage)
        ];
        return json_encode($widgetJson, JSON_UNESCAPED_UNICODE);
    }

    private function getFormattedFeatures(Widget $widget, Brand $brand, ?Country $country, string $source, ?Language $language): array
    {

        $features = [];
        foreach ($widget->getFeaturesConfiguration()['features'] as $feature) {
            $featureCode = StringHelper::cleanString($feature['code']);
            $widgetFeature = $this->widgetFeatureRepository->findOneBy(['code' => $featureCode, 'widget' => $widget, 'brand' => $brand, 'country' => $country, 'source' => $source]);

            $features[] = [
                'name' => $featureCode,
                'description' => $feature['description'] ?? '',
                'enabled' => isset($feature['enabled']) && $feature['enabled']
                    ? ($widgetFeature ? $widgetFeature->isEnabled() ?? false : false)
                    : true,
                'attributes' => $widgetFeature ? $this->getFormatedFeatureAttributes($widgetFeature, $language) : [],
            ];
        }

        return $features;
    }

    private function getFormatedFeatureAttributes(WidgetFeature $widgetFeature, Language $language): array
    {
        $attributes = [];
        foreach ($widgetFeature->getAttributes() as $attribute) {
            if (
                !$attribute->getLanguage() ||
                $language === $attribute->getLanguage()
            ) {
                if ($attribute->getType() == self::MULTI_FIELDS_TYPE) {
                    $multiFieldsData = json_decode($attribute->getValue(), true);
                    $multiFieldsValues = $this->removeKeys($multiFieldsData);
                    foreach ($multiFieldsValues as $multiFieldValue) {
                        foreach ($multiFieldValue as $fieldname => $fieldvalue) {
                            if (is_array($fieldvalue)) {
                                if ($fieldvalue[$language->getCode()]) {
                                    $multiFieldsValue[$fieldname] = $fieldvalue[$language->getCode()];
                                } else {
                                    $multiFieldsValue[$fieldname] = $fieldvalue;
                                }
                            } else {
                                $multiFieldsValue[$fieldname] = $fieldvalue;
                            }
                        }

                        if (!isset($attributes[$attribute->getName()])) {
                            $attributes[$attribute->getName()] = [];
                        }
                        if (is_array($attributes[$attribute->getName()])) {
                            $attributes[$attribute->getName()][] = $multiFieldsValue;
                        } else {
                            $attributes[$attribute->getName()] = $multiFieldsValue;
                        }
                    }
                } else {
                    $attributes[$attribute->getName()] = $attribute->getValue();
                }
            }
        }
        return $attributes;
    }

    public function isWidgetEnabled(?Widget $widget, ?Brand $brand, ?Country $country, ?string $source): bool
    {
        $widgetData = $this->widgetDataRepository->findOneBy(['widget' => $widget, 'brand' => $brand, 'country' => $country, 'source' => $source]);
        if ($widgetData === null) {
            return false;
        }
        return $widgetData->isEnabled() !== null ? $widgetData->isEnabled() : false;
    }

    private function createWidgetDataIfNotExists(Brand $brand, Country $country, string $source, widget $widget): WidgetData
    {
        $widgetData = $this->widgetDataRepository->findOneBy(['brand' => $brand, 'country' => $country, 'source' => $source, 'widget' => $widget]);

        if (!$widgetData) {
            $widgetData = (new widgetData())
                ->setEnabled(false)
                ->setBrand($brand)
                ->setCountry($country)
                ->setSource($source)
                ->setWidget($widget);

            $this->objectManager->persist($widgetData);
            $this->objectManager->flush();
        }

        return $widgetData;
    }

    public function saveWidgetsLabels(array $labels, array $sources, Widget $widget): Widget
    {
        $brands = $this->brandRepository->findAll();
        foreach ($sources as $source) {
            foreach ($labels as $label) {
                foreach ($brands as $brand) {
                    $translationKey = $this->translationKeyFactory->createTranslationKey();
                    $translationKey->setLabelKey($label)
                        ->setChannel($source)
                        ->setBrand($brand)
                        ->setWidget($widget);
                    $this->objectManager->persist($translationKey);
                }
            }
        }
        return $widget;
    }

    public function editWidget($file, Widget $widget): Widget
    {
        // Get the original widget from the database
        $originalWidget = $this->objectManager->getUnitOfWork()->getOriginalEntityData($widget);
        $originalName = $originalWidget['name'];

        $widgetNew = $widget->getName() ?? '';

        //Check if the widget has any associated features or data
        $widgetFeaturesCount = (int) $widget->getWidgetFeatures()->count();
        $widgetDataCount = (int) $widget->getWidgetData()->count();

        // If there are no associated features or data, update the name and other attributes
        if ($widgetFeaturesCount === 0 && $widgetDataCount === 0) {
            $widget->setName($widgetNew);
        } else {

            $widget->setName($originalName);
        }

        // Update features configuration if a file is provided
        if ($file) {
            $content = file_get_contents($file);
            $content = json_decode($content, true);
            $widget->setFeaturesConfiguration($content);
        }
        // Persist and flush changes to the database
        $this->objectManager->persist($widget);
        $this->objectManager->flush();
        return $widget;
    }

    public function getLabels(?widget $widget, ?Brand $brand, ?Country $country, ?string $source, ?Language $localLanguage, ?Language $referenceLanguage): array
    {
        return $this->labelTranslationManager->getLabelsByWidget($widget, $brand, $country, $source, $localLanguage, $referenceLanguage);
    }

    private function removeKeys(?array $lists = [])
    {
        for ($i = 0; $i <= count($lists); $i++) {
            unset($lists[$i]['image_input']);
            unset($lists[$i]['type']);
        }

        return $this->removeAllNullValues($lists);
    }

    private function removeAllNullValues(?array $array = []): ?array
    {
        return array_filter($array, function ($item) {
            if (is_array($item)) {
                foreach ($item as $value) {
                    if (!is_null($value)) {
                        return true;
                    }
                }
                return false;
            }
        }) ?? [];
    }


    /**
     * @param mixed $file
     * 
     * @return array
     */
    public function isValidWidgetFile($file): array
    {
        $errors = [];
        // Load the JSON data from the $file
        $jsonData = file_get_contents($file);

        if (!$this->isValidJson($jsonData)) {
            $errors[] = "The file content is not a valid JSON.";
            return $errors;
        }

        // Decode the JSON data into an array or object
        $widgetData = json_decode($jsonData, true);

        // Create an instance of the WidgetData model and populate it with the decoded data
        $widgetDataModel = new WidgetDataModel();
        $widgetDataModel->labels = $widgetData['labels'] ?? [];
        $widgetDataModel->sources = $widgetData['sources'] ?? [];

        foreach ($widgetData['features'] ?? [] as $featureData) {
            $featureDataModel = new FeatureDataModel();
            $featureDataModel->code = $featureData['code'] ?? [];
            $featureDataModel->title = $featureData['title'] ?? [];

            foreach ($featureData['fields'] as $fieldData) {
                $fieldDataModel = new FieldDataModel();
                $fieldDataModel->name = $fieldData['name'] ?? [];
                $fieldDataModel->type = $fieldData['type'] ?? [];
                $fieldDataModel->section = $fieldData['section'] ?? [];
                $fieldDataModel->translatable = $fieldData['translatable'] ?? [];

                $featureDataModel->fields[] = $fieldDataModel;
            }

            $widgetDataModel->features[] = $featureDataModel;
        }
        // Validate the model
        $violations = $this->validator->validate($widgetDataModel);

        if (count($violations) > 0) {
            foreach ($violations as $violation) {
                $errors[] = $violation->getMessage();
            }
            return $errors;
        }

        return [];
    }

    /**
     * @param string $value
     * 
     * @return bool
     */
    private function isValidJson(string $value): bool
    {
        json_decode($value);
        return (json_last_error() === JSON_ERROR_NONE);
    }

    public function isWidgetPublished(Widget $widget): bool
    {
        $publishedData = $this->widgetDataRepository->findByWidget($widget);
        return !empty($publishedData);
    }

    public function makePackage(Widget $widget)
    {
        $widgetRows = $this->widgetRepository->findBy(['name' => $widget->getName()]) ?? [];

        $encoders = [new JsonEncoder()];
        $normalizers = [$this->normalizer];
        $serializer = new Serializer($normalizers, $encoders);
        $jsonContent = $serializer->serialize($widgetRows[0], 'json', [
            'groups' => ['package_export'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
            'json_encode_options' => JSON_PRETTY_PRINT,
        ]);

        return $jsonContent;
    }

    public function importWidget(string $jsonContent): array
    {
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [false, "Invalid Json format"];
        }
        try {
            $encoders = [new JsonEncoder()];

            $normalizers = [$this->normalizer];

            $serializer = new Serializer($normalizers, $encoders);

            $widget = $serializer->deserialize($jsonContent, Widget::class, 'json', [
                'groups' => ['package_export'],
                AbstractNormalizer::CALLBACKS => [
                    'brand' => function ($brandData, $widget) {
                        return $brandData != null && $brandData->getCode() ? $this->objectManager->getRepository(Brand::class)->findOneBy(['code' => $brandData->getCode()]) : null;
                    },
                    'country' => function ($countryData, $widget) {
                        return $countryData != null && $countryData->getCode() ? $this->objectManager->getRepository(Country::class)->findOneBy(['code' => $countryData->getCode()]) : null;
                    },
                    'language' => function ($languageData, $widget) {
                        return $languageData != null && $languageData->getCode() ? $this->objectManager->getRepository(Language::class)->findOneBy(['code' => $languageData->getCode()]) : null;
                    },
                ],
            ]);

            foreach ($widget->getWidgetFeatures() as $feature) {
                foreach ($feature->getAttributes() as $attribute) {
                    $this->objectManager->persist($attribute);
                }
            }

            foreach ($widget->getWidgetData() as $widgetData) {
                $this->objectManager->persist($widgetData);
            }

            $this->objectManager->persist($widget);
            $this->objectManager->flush();
            return [true, "Widget imported successfully"];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    public function getAccessLog(Widget $widget, Brand $brand, Country $country): array
    {
        try {
            $widgetFeatureAttributeIds = $this->widgetFeatureAttributeRepository->getIds($widget);
            $accessLogs = $this->accessLogRepository->getWidgetLogs($widgetFeatureAttributeIds, $brand, $country);
            //Data formation
            $groupedLogs = [];
            $count = 0;
            foreach ($accessLogs['results'] as $accessLog) {
                $logDate = $accessLog[0]->getTimestamp()->format('Y-m-d H:i:s');
                $languageCode = $accessLog['label'] ?? "-";
                $featureName = $accessLog['featureName'];
                $channel = $accessLog['channel'];
                if (!isset($groupedLogs[$logDate][$featureName][$languageCode][$channel])) {
                    $count += 1;
                    $groupedLogs[$logDate][$featureName][$languageCode][$channel] = [
                        'before' => [],
                        'after' => [],
                        'username' => $accessLog[0]->getUsername(),
                        'timestamp' => $accessLog[0]->getTimestamp()->format('Y-m-d H:i:s'),
                    ];
                }
                $before = $accessLog[0]->getValuesBefore();
                $after = $accessLog[0]->getValuesAfter();
                $groupedLogs[$logDate][$featureName][$languageCode][$channel]['before'][$accessLog['name']] = $before['value'] ?? '';
                $groupedLogs[$logDate][$featureName][$languageCode][$channel]['after'][$accessLog['name']] = $after['value'] ?? '';
            }

            $output = array(
                'data' => $groupedLogs,
                'recordsFiltered' => $count,
                'recordsTotal' => $count
            );
            return $output;
        } catch (\Throwable $e) {
            $this->logger->error(__METHOD__ . ': getting the access log data for widget', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);
            return array(
                'data' => [],
                'recordsFiltered' => 0,
                'recordsTotal' => 0
            );
        }
    }

    public function releaseMultiple(array $brands, array $countries, array $widgets, array $sources): WSResponse
    {
        try {
            $widgetDatas = $this->widgetDataRepository->findByBrandCountryWidget($brands, $countries, $widgets, $sources);
            $language = $this->languageRepository->findOneBy(['code' => 'en']);

            foreach ($widgetDatas as $widgetData) {
                $this->messageBus->dispatch(new PublishWidgetMessage($widgetData->getId()));
                $this->logger->info("Widget data published for widget id: " . $widgetData->getId());
            }

            return new WSResponse(Response::HTTP_OK, true);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function publish(WidgetData $widgetData, Site $site): WSResponse
    {
        try {
            $languages = $site->getLanguages()->isEmpty() ? [$site->getPreferedLanguage()] : $site->getLanguages();
            $referenceLanguage = $site->getPreferedLanguage();
            $brand = $widgetData->getBrand();
            $country = $widgetData->getCountry();
            $source = $widgetData->getSource();

            foreach ($languages as $language) {
                $data = $this->getJsonWidgetData($widgetData, $brand, $country, $source, $language, $referenceLanguage);

                $fileName = $this->getWidgetFileName(
                    $brand,
                    $country,
                    $language,
                    $source,
                    $widgetData->getWidget()->getName() ?? ''
                );
                $this->logger->debug("publishing data on file $fileName for $brand/$country/$source/$language data: $data");
                $status = $this->s3Service->putObject($fileName, $data, $this->settingsBucket);
                $this->logger->info("$fileName was uploaded: $status");
            }
            return new WSResponse(Response::HTTP_OK, true);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
