<?php

namespace App\Service;

use App\Helpers\WSResponse;
use App\Model\MongoQueryModel;
use App\Helpers\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

/**
 * This service helps to interact with mongoAtlas api.
 */
class MongoAtlasQueryService
{
    use LoggerTrait;

    private $query;

    public function __construct(private NormalizerInterface $normalizer, private MongoAtlasApiConnector $connector, private string $dataSource, string $database)
    {
        $this->query = self::buildQuery($dataSource, $database);
    }

    /**
     * Build mongo query.
     */
    public static function buildQuery(string $dataSource, string $database) : MongoQueryModel
    {
        return (new MongoQueryModel())
            ->setDataSource($dataSource)
            ->setDatabase($database);
    }

    public function find(string $collection, ?array $filter) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter ?? []);

        return $this->execute('find');
    }

    public function findOne(string $collection, ?array $filter) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter ?? []);

        return $this->execute('findOne');
    }

    public function delete(string $collection, ?array $filter) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter ?? []);

        return $this->execute('deleteOne');
    }

    public function deleteOne(string $collection, ?array $filter) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter ?? []);

        return $this->execute('deleteOne');
    }

    public function aggregate(string $collection, ?array $pipeline) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setPipeline($pipeline);

        return $this->execute('aggregate');
    }

    public function updateMany(string $collection, array $filter, array $fields, ?bool $upsert = false) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter)
            ->setUpdate(['$set' => $fields])
            ->setUpsert($upsert);

        return $this->execute('updateMany');
    }

    public function updateOne(string $collection, array $filter, array $fields, ?bool $upsert = false) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter)
            ->setUpdate(['$set' => $fields])
            ->setUpsert($upsert);

        return $this->execute('updateOne');
    }

    public function updatePush(string $collection, array $filter, array $fields, ?bool $upsert = false) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setFilter($filter)
            ->setUpdate(['$push' => $fields]);

        return $this->execute('updateOne');
    }

    public function insertOne(string $collection, array $document) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setDocument($document);

        return $this->execute('insertOne');
    }

    public function insertMany(string $collection, array $documents) : WSResponse
    {
        $this->query->setCollection($collection)
            ->setDocuments($documents);

        return $this->execute('insertMany');
    }

    public function updateAndInsertMany(
        string $collection,
        array $filters,
        array $fields,
        ?bool $upsert = false
    ) : WSResponse {
        foreach ($fields as $field)
        {
            $filter = [];
            foreach ($filters as $name)
            {
                $filter[$name] = $field[$name] ?? '';
            }
            $isUpdated = $this->updateMany($collection, $filter, $field, $upsert)->getCode() == Response::HTTP_OK;
            if (! $isUpdated)
            {
                return new WSResponse(Response::HTTP_BAD_REQUEST, 'There is an issue on updating data.');
            }
        }
        return new WSResponse(Response::HTTP_OK, 'Data updated successfully.');
    }

    /**
     * Execute Mongo request.
     */
    private function execute(string $action) : WSResponse
    {
        try
        {
            $this->logger->info('MongoAtlasQueryService::execute : ' . $action);
            $endpoint = $this->connector->getEndpoint($action);
            $collection = $this->normalizer->normalize($this->query, 'array', ['groups' => ['default', $action]]);

            // Fix empty filter arrays to be objects for MongoDB Atlas API
            if (isset($collection['filter']) && is_array($collection['filter']) && empty($collection['filter'])) {
                $collection['filter'] = new \stdClass();
            }

            return $this->connector->call(Request::METHOD_POST, $endpoint, ['json' => $collection]);
        } catch (\Exception $e)
        {
            $this->logger->error('catched Exception in MongoAtlasQueryService::execute :' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function findOrCreate(string $collection, array $filter, array $document) : WSResponse
    {
        $response = $this->find($collection, $filter);
        $data = $response->getData();


        if ($response->getCode() === Response::HTTP_OK && strlen($data) > 20)
        {
            $response = $this->updateOne($collection, $filter, $document);
            return $response;
        }
        return $this->insertOne($collection, $document);
    }

    public function findDuplicateKeys(string $collectionName, array $keys): array
{
    // Build the query to search for keys in the 'lcdv' array
    $filter = [
        'lcdv' => [
            '$elemMatch' => [
                '$in' => $keys  
            ]
        ]
    ];

    // Execute the query using MongoDB connector
    $result = $this->find($collectionName, $filter);
    if($result->getCode() == 400 ) return [];
    $jsonResult = json_decode($result->getData(), true);

    // Extract duplicate keys from the result
    $duplicateKeys = [];
    foreach ($jsonResult['documents'] as $document) {
        if (!isset($document['lcdv'])) {
            continue;
        }
        foreach ($keys as $key) {
            if (in_array($key, $document['lcdv'])) {
                $duplicateKeys[] = $key;
            }
        }
    }

    // Return only unique duplicate keys
    return array_unique($duplicateKeys);
}


}
