<?php

namespace App\Entity;

use App\Repository\SpsEligibilityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: SpsEligibilityRepository::class)]
#[ORM\Table(name: 'sps_eligibility')]
#[ORM\HasLifecycleCallbacks]
class SpsEligibility
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank(message: 'sps_eligibility_lcdv_codes_not_blank')]
    private ?string $lcdvCodes = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank(message: 'sps_eligibility_rules_not_blank')]
    private ?string $eligibilityRules = null;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank(message: 'sps_eligibility_type_not_blank')]
    private ?string $type = null;

    #[ORM\Column(type: 'boolean', name: 'eligibility_disclaimer', options: ['default' => false])]
    private bool $eligibilityDisclaimer = false;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: 'datetime', name: 'updated_at', nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLcdvCodes(): ?string
    {
        return $this->lcdvCodes;
    }

    public function setLcdvCodes(string $lcdvCodes): self
    {
        $this->lcdvCodes = $lcdvCodes;
        return $this;
    }

    /**
     * Get LCDV codes as array
     */
    public function getLcdvCodesArray(): array
    {
        if (empty($this->lcdvCodes)) {
            return [];
        }
        return array_filter(array_map('trim', explode(',', $this->lcdvCodes)));
    }

    /**
     * Set LCDV codes from array
     */
    public function setLcdvCodesFromArray(array $codes): self
    {
        $this->lcdvCodes = implode(', ', array_filter($codes));
        return $this;
    }

    public function getEligibilityRules(): ?string
    {
        return $this->eligibilityRules;
    }

    public function setEligibilityRules(string $eligibilityRules): self
    {
        $this->eligibilityRules = $eligibilityRules;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function isEligibilityDisclaimer(): bool
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(bool $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }
}
