<?php

namespace App\Repository;

use App\Entity\LcdvLabel;
use App\Entity\VehicleLabel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry as PersistenceManagerRegistry;

/**
 * @method LcdvLabel|null find($id, $lockMode = null, $lockVersion = null)
 * @method LcdvLabel|null findOneBy(array $criteria, array $orderBy = null)
 * @method LcdvLabel[]    findAll()
 * @method LcdvLabel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LcdvLabelRepository extends ServiceEntityRepository
{
    public function __construct(PersistenceManagerRegistry $registry)
    {
        parent::__construct($registry, LcdvLabel::class);
    }

    public function deleteByBrandAndLabel($brand, $label) {
        try {
            return $this->createQueryBuilder('vehicle_model')
                ->delete()
                ->where('vehicle_model.label = :label')
                ->andWhere('vehicle_model.brand = :brand')
                ->setParameter('label', $label)
                ->setParameter('brand', $brand)
                ->getQuery()->execute();
        }
        catch (\Exception $e) {
            return 0;
        }
    }

    public function checkLabelUsageForUpdateValidation($label, $brand, $ids)
    {
        return $this->createQueryBuilder('l')
            ->where('l.label = :label')
            ->andWhere('l.brand = :brand')
            ->andWhere('l.id not in (:ids)')
            ->setParameter('label', $label)
            ->setParameter('brand', $brand)
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getResult();
    }

    public function getLCDVs($lcdv)
    {
        $lcdv = substr($lcdv, 0, 2);

        return $this->createQueryBuilder('l')
            ->where("l.lcdv LIKE :lcdv")
            ->setParameter('lcdv', $lcdv.'%')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param  string  $lcdv
     *
     * @return \Doctrine\Common\Collections\ArrayCollection
     */
    public function getLcdvLabelsWhereInLcdv(string $lcdv, $brand): ArrayCollection
    {
        // Prepare the parameter
        $lcdvs = [];

        foreach (range(2, strlen($lcdv)) as $length) {
            $lcdvs[] = substr($lcdv, 0, $length);
        }

        // Query
        $qb = $this->createQueryBuilder('l');

        $results = $qb
            ->join('l.vehicleLabel', 'lv')
            ->where($qb->expr()->in('l.lcdv', ':lcdvs'))
            ->andWhere('lv.brand = :brand')
            ->setParameter('lcdvs', array_filter($lcdvs))
            ->setParameter('brand', $brand)
            ->orderBy('length(l.lcdv)', 'desc')
            ->getQuery()
            ->getResult();

        return new ArrayCollection($results);
    }

    public function findOneLcdvLabelByBrand($fields)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.lcdv =:lcdv')
            ->join('l.vehicleLabel', 'lv')
            ->andWhere('lv.brand = :brand')
            ->setParameter('lcdv', $fields['lcdv'])
            ->setParameter('brand', (isset($fields['vehicleLabel']) && $fields['vehicleLabel'] instanceof VehicleLabel) ? $fields['vehicleLabel']->getBrand() : null)
            ->getQuery()
            ->getResult();
    }
}
