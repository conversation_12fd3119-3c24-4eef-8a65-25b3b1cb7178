<?php

namespace App\Repository;

use App\Entity\SpsEligibility;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SpsEligibility>
 *
 * @method SpsEligibility|null find($id, $lockMode = null, $lockVersion = null)
 * @method SpsEligibility|null findOneBy(array $criteria, array $orderBy = null)
 * @method SpsEligibility[]    findAll()
 * @method SpsEligibility[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SpsEligibilityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SpsEligibility::class);
    }

    /**
     * Find all SPS Eligibility rules ordered by creation date
     */
    public function findAllOrdered(): array
    {
        return $this->createQueryBuilder('s')
            ->orderBy('s.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find SPS Eligibility rules by type
     */
    public function findByType(string $type): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.type = :type')
            ->setParameter('type', $type)
            ->orderBy('s.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find SPS Eligibility rules containing specific LCDV code
     */
    public function findByLcdvCode(string $lcdvCode): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.lcdvCodes LIKE :lcdvCode')
            ->setParameter('lcdvCode', '%' . $lcdvCode . '%')
            ->orderBy('s.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search SPS Eligibility rules by multiple criteria
     */
    public function search(?string $lcdvCode = null, ?string $type = null, ?bool $disclaimer = null): array
    {
        $qb = $this->createQueryBuilder('s');

        if ($lcdvCode) {
            $qb->andWhere('s.lcdvCodes LIKE :lcdvCode')
               ->setParameter('lcdvCode', '%' . $lcdvCode . '%');
        }

        if ($type) {
            $qb->andWhere('s.type = :type')
               ->setParameter('type', $type);
        }

        if ($disclaimer !== null) {
            $qb->andWhere('s.eligibilityDisclaimer = :disclaimer')
               ->setParameter('disclaimer', $disclaimer);
        }

        return $qb->orderBy('s.createdAt', 'DESC')
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Get distinct types used in SPS Eligibility rules
     */
    public function getDistinctTypes(): array
    {
        $result = $this->createQueryBuilder('s')
            ->select('DISTINCT s.type')
            ->where('s.type IS NOT NULL')
            ->orderBy('s.type', 'ASC')
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'type');
    }
}
