<?php

namespace App\Form;

use App\Entity\Brand;
use App\Entity\Country;
use App\Repository\ChannelRepository;
use App\Repository\WidgetRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

class WidgetPublishFormType extends AbstractType
{
    public function __construct(private EntityManagerInterface $entityManager, private ChannelRepository $channelRepository, private WidgetRepository $widgetRepository) {}

    const CUSTOM_SELECT_CSS = "no-background filterInSelect custom-select";

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $site = $options['site'];
        $profile = $options['profile'];
        $channels = $this->channelRepository->findAll();
        $channelNames = array_map(function ($channel) {
            return $channel->getName();
        }, $channels);

        $widget = $this->widgetRepository->findAll();
        $widgetChoices = $this->getChoices($widget, fn($widget) => $widget->getName());

        $channelHashmap = array_combine($channelNames, $channelNames);

        $brands = $this->entityManager->getRepository(Brand::class)->findAll();
        $brandChoices = $this->getChoices($brands, fn($brand) => $brand->getName());

        $countries = $this->entityManager->getRepository(Country::class)->findAll();
        $countryChoices = $this->getChoices($countries, fn($country) => $country->getName());


        $builder->add('brand', ChoiceType::class, [
            'label' => 'Brand',
            'multiple' => true,
            'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
            'required' => false,
            'mapped' => false,
            'choices' => $brandChoices,
        ])
            ->add('brandList', ChoiceType::class, [
                'label' => 'label',
                'choice_label' => false,
                'multiple' => true,
                'mapped' => false,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'choices' => [],
            ]);
        $builder->add('country', ChoiceType::class, [
            'label' => 'Country',
            'multiple' => true,
            'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
            'required' => false,
            'mapped' => false,
            'choices' => $countryChoices,
        ])
            ->add('countryList', ChoiceType::class, [
                'label' => 'label',
                'choice_label' => false,
                'multiple' => true,
                'mapped' => false,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'choices' => [],
            ]);
        $builder->add('widget', ChoiceType::class, [
            'label' => 'Widget',
            'multiple' => true,
            'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
            'required' => false,
            'mapped' => false,
            'choices' => $widgetChoices,
        ])
            ->add('widgetList', ChoiceType::class, [
                'label' => 'label',
                'choice_label' => false,
                'multiple' => true,
                'mapped' => false,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'choices' => [],
            ]);
        $builder->add('source', ChoiceType::class, [
            'choices' => $channelHashmap,
            'required' => false,
            'multiple' => true,
            'mapped' => true,
        ]);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) use ($brandChoices, $countryChoices, $widgetChoices) {
            $form = $event->getForm();
            $data = $event->getData();
            $brandList = $data['brandList'] ?? [];
            $countryList = $data['countryList'] ?? [];
            $widgetList = $data['widgetList'] ?? [];

            $form->add('brand', ChoiceType::class, [
                'choices' => $brandChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            if ($brandList) {
                $form->add('brandList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $brandList,
                    'multiple' => true,
                ]);
            }
            $form->add('country', ChoiceType::class, [
                'choices' => $countryChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            if ($countryList) {
                $form->add('countryList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $countryList,
                    'multiple' => true,
                ]);
            }
            if ($widgetList) {
                $form->add('widgetList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $widgetList,
                    'multiple' => true,
                ]);
            }

            $form->add('widget', ChoiceType::class, [
                'choices' => $widgetChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            $event->setData($data);
        });
    }

    public function getChoices($entities)
    {
        $names = array_map(fn($entity) => $entity->getName(), $entities);
        $ids = array_map(fn($entity) => $entity->getId(), $entities);

        return array_combine($names, $ids);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'site' => null,
            'profile' => null
        ]);
    }
}
