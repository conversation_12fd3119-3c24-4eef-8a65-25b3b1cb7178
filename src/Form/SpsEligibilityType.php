<?php

namespace App\Form;

use App\Entity\SpsEligibility;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class SpsEligibilityType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('lcdvCodes', TextType::class, [
                'label' => 'LCDV24-4digits',
                'help' => 'Enter multiple LCDV codes separated by commas (e.g., 1234, 5678, 9012)',
                'attr' => [
                    'placeholder' => 'Enter LCDV codes separated by commas',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'LCDV codes are required'),
                    new Assert\Regex([
                        'pattern' => '/^[0-9A-Za-z\s,]+$/',
                        'message' => 'LCDV codes can only contain letters, numbers, spaces, and commas'
                    ])
                ]
            ])
            ->add('eligibilityRules', TextareaType::class, [
                'label' => 'Eligibility rules based on LCDV attribute',
                'help' => 'Describe the eligibility rules for these LCDV codes',
                'attr' => [
                    'rows' => 5,
                    'placeholder' => 'Enter eligibility rules...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Eligibility rules are required'),
                    new Assert\Length([
                        'min' => 10,
                        'max' => 2000,
                        'minMessage' => 'Eligibility rules must be at least {{ limit }} characters long',
                        'maxMessage' => 'Eligibility rules cannot be longer than {{ limit }} characters'
                    ])
                ]
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type',
                'choices' => [
                    'Standard' => 'standard',
                    'Premium' => 'premium',
                    'Basic' => 'basic',
                    'Advanced' => 'advanced',
                    'Custom' => 'custom'
                ],
                'placeholder' => 'Select a type',
                'attr' => [
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Type is required')
                ]
            ])
            ->add('eligibilityDisclaimer', CheckboxType::class, [
                'label' => 'Eligibility Disclaimer',
                'help' => 'Check if this eligibility rule requires a disclaimer',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SpsEligibility::class,
        ]);
    }
}
