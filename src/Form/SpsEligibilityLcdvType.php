<?php

namespace App\Form;

use App\Document\SpsEligibilityLcdv;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Form type for SPS Eligibility LCDV rules
 *
 * This form handles the creation and editing of LCDV-based eligibility rules
 * for the SPS (Smart Phone Services) system. LCDV codes are 4-digit vehicle
 * identification codes used to determine service eligibility.
 *
 * <AUTHOR> BO Development Team
 * @since 2024-06-27
 */
class SpsEligibilityLcdvType extends AbstractType
{
    /**
     * Build the form for LCDV eligibility rules
     *
     * @param FormBuilderInterface $builder The form builder
     * @param array $options Form options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('lcdvCodesString', TextType::class, [
                'label' => 'LCDV Codes (4-digit codes)',
                'help' => 'Enter LCDV codes separated by commas (e.g., 1234, 5678, 9012)',
                'attr' => [
                    'placeholder' => 'Enter LCDV codes separated by commas...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'LCDV codes are required'),
                    new Assert\Regex([
                        'pattern' => '/^[0-9A-Za-z\s,]+$/',
                        'message' => 'LCDV codes can only contain letters, numbers, spaces, and commas'
                    ])
                ]
            ])
            ->add('eligibilityRules', TextareaType::class, [
                'label' => 'Eligibility rules based on LCDV attribute',
                'help' => 'Describe the eligibility rules for these LCDV codes',
                'attr' => [
                    'rows' => 5,
                    'placeholder' => 'Enter eligibility rules...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Eligibility rules are required'),
                    new Assert\Length([
                        'min' => 10,
                        'max' => 2000,
                        'minMessage' => 'Eligibility rules must be at least {{ limit }} characters long',
                        'maxMessage' => 'Eligibility rules cannot be longer than {{ limit }} characters'
                    ])
                ]
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type',
                'help' => 'Select the type of eligibility rule',
                'choices' => [
                    'Standard' => 'standard',
                    'Premium' => 'premium',
                    'Special' => 'special',
                    'Custom' => 'custom'
                ],
                'placeholder' => 'Choose a type...',
                'attr' => [
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Type is required')
                ]
            ])
            ->add('eligibilityDisclaimer', CheckboxType::class, [
                'label' => 'Eligibility Disclaimer Required',
                'help' => 'Check if a disclaimer is required for this eligibility rule',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ]
            ]);
    }

    /**
     * Configure form options
     *
     * @param OptionsResolver $resolver The options resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SpsEligibilityLcdv::class,
        ]);
    }
}
