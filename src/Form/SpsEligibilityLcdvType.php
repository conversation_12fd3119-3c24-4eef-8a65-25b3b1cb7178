<?php

namespace App\Form;

use App\Document\SpsEligibilityLcdv;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Form type for SPS Eligibility LCDV rules
 *
 * This form handles the creation and editing of LCDV-based eligibility rules
 * for the SPS (Smart Phone Services) system. LCDV codes are 4-digit vehicle
 * identification codes used to determine service eligibility.
 *
 * <AUTHOR> Shrivastava
 * @since 2024-06-27
 */
class SpsEligibilityLcdvType extends AbstractType
{
    /**
     * Build the form for LCDV eligibility rules
     *
     * @param FormBuilderInterface $builder The form builder
     * @param array $options Form options (unused in this implementation)
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('lcdvCodesString', TextType::class, [
                'label' => 'LCDV Codes (up to 20 characters each)',
                'help' => 'Enter LCDV codes separated by commas (e.g., ABC123, XYZ789, LONG1234567890)',
                'attr' => [
                    'placeholder' => 'Enter LCDV codes separated by commas...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'LCDV codes are required'),
                    new Assert\Regex([
                        'pattern' => '/^[0-9A-Za-z\s,]+$/',
                        'message' => 'LCDV codes can only contain letters, numbers, spaces, and commas'
                    ]),
                    new Assert\Callback([
                        'callback' => function ($value, $context) {
                            if ($value) {
                                $codes = array_map('trim', explode(',', $value));
                                foreach ($codes as $code) {
                                    if (strlen($code) > 20) {
                                        $context->buildViolation('Each LCDV code must be 20 characters or less. "{{ code }}" is {{ length }} characters.')
                                            ->setParameter('{{ code }}', $code)
                                            ->setParameter('{{ length }}', strlen($code))
                                            ->addViolation();
                                    }
                                    if (strlen($code) < 1) {
                                        $context->buildViolation('LCDV codes cannot be empty.')
                                            ->addViolation();
                                    }
                                }
                            }
                        }
                    ])
                ]
            ])
            ->add('eligibilityRule', TextareaType::class, [
                'label' => 'Eligibility rules based on LCDV attribute',
                'attr' => [
                    'rows' => 5,
                    'placeholder' => 'Enter eligibility rules...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Eligibility rules are required'),
                    new Assert\Length([
                        'min' => 10,
                        'max' => 2000,
                        'minMessage' => 'Eligibility rules must be at least {{ limit }} characters long',
                        'maxMessage' => 'Eligibility rules cannot be longer than {{ limit }} characters'
                    ])
                ]
            ])
            ->add('type', TextType::class, [
                'label' => 'Type',
                'help' => 'Enter the type of eligibility rule (e.g., spsGeneric, o2x, fiat500)',
                'attr' => [
                    'placeholder' => 'Enter type (e.g., spsGeneric, o2x, fiat500)...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Type is required'),
                    new Assert\Regex([
                        'pattern' => '/^[a-zA-Z0-9_-]+$/',
                        'message' => 'Type can only contain letters, numbers, underscores, and hyphens'
                    ])
                ]
            ])
            ->add('eligibilityDisclaimer', TextType::class, [
                'label' => 'Eligibility Disclaimer',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Enter disclaimer text (optional)...',
                    'class' => 'form-control'
                ]
            ]);
    }

    /**
     * Configure form options
     *
     * @param OptionsResolver $resolver The options resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SpsEligibilityLcdv::class,
        ]);
    }
}
