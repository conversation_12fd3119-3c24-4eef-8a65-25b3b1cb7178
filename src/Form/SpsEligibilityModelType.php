<?php

namespace App\Form;

use App\Document\SpsEligibilityModel;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Form type for SPS Eligibility Model rules
 *
 * This form handles the creation and editing of vehicle model-based eligibility rules
 * for the SPS (Smart Phone Services) system. Model rules define eligibility based on
 * specific vehicle models and minimum model years.
 *
 * <AUTHOR> Shri<PERSON>tava
 * @since 2024-06-27
 */
class SpsEligibilityModelType extends AbstractType
{
    /**
     * Build the form for Model eligibility rules
     *
     * @param FormBuilderInterface $builder The form builder
     * @param array $options Form options (unused in this implementation)
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('modelsString', TextType::class, [
                'label' => 'Vehicle Models',
                'help' => 'Enter vehicle model names separated by commas (e.g., fiat500, spsGeneric, model3)',
                'attr' => [
                    'placeholder' => 'Enter model names separated by commas...',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Vehicle models are required'),
                    new Assert\Regex([
                        'pattern' => '/^[a-zA-Z0-9\s,_-]+$/',
                        'message' => 'Model names can only contain letters, numbers, spaces, commas, underscores, and hyphens'
                    ])
                ]
            ])
            ->add('eligibilityRules', TextareaType::class, [
                'label' => 'Eligibility Rules',
                'help' => 'Enter detailed description of eligibility rules for these models',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Enter eligibility rules description...',
                    'class' => 'form-control',
                    'rows' => 4
                ]
            ])
            ->add('modelYearFrom', IntegerType::class, [
                'label' => 'Eligibility rules based on Model year >=',
                'help' => 'Enter the minimum model year for eligibility (e.g., 2024)',
                'attr' => [
                    'placeholder' => 'Enter minimum model year...',
                    'class' => 'form-control',
                    'min' => 1900,
                    'max' => 2050
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Model year is required'),
                    new Assert\Range([
                        'min' => 1900,
                        'max' => 2050,
                        'notInRangeMessage' => 'Model year must be between {{ min }} and {{ max }}'
                    ])
                ]
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type',
                'help' => 'Select the type of eligibility rule',
                'choices' => [
                    'fiat500' => 'fiat500',
                    'spsGeneric' => 'spsGeneric',
                    'Standard' => 'standard',
                    'Premium' => 'premium',
                    'Special' => 'special',
                    'Custom' => 'custom'
                ],
                'placeholder' => 'Choose a type...',
                'attr' => [
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Assert\NotBlank(message: 'Type is required')
                ]
            ])
            ->add('eligibilityDisclaimer', CheckboxType::class, [
                'label' => 'Eligibility Disclaimer Required',
                'help' => 'Check if a disclaimer is required for this eligibility rule',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ]
            ]);
    }

    /**
     * Configure form options
     *
     * @param OptionsResolver $resolver The options resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SpsEligibilityModel::class,
        ]);
    }
}
