<?php

namespace App\Controller;

use App\Service\WidgetManager;
use App\Entity\Widget;
use App\Form\WidgetFormType;
use App\Form\WidgetPublishFormType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use App\Security\User;
use Exception;

#[Route('/widget', name: 'widget_admin_')]
class WidgetAdminController extends AbstractController
{
    #[Route('/', name: 'list')]
    public function index(#[CurrentUser] User $user, WidgetManager $widgetManager): Response
    {
        $isSuperAdmin = $user->getProfile()->isSuperAdministrator();
        return $this->render('widget/index.html.twig', [
            'widgets' => $widgetManager->list(),
            'isSuperAdmin' => $isSuperAdmin,
            'widgetManager' => $widgetManager,
        ]);
    }

    #[Route(path: '/create', name: 'create', methods: ['POST', 'GET'])]
    public function create(Request $request, WidgetManager $widgetManager): Response
    {
        $widget = new Widget();
        $form = $this->createForm(WidgetFormType::class, $widget);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();
            $errors = $widgetManager->isValidWidgetFile($file);
            if (!empty($errors)) {
                // Handle validation errors
                foreach ($errors as $error) {
                    $this->addFlash('danger', $error);
                }
                return $this->render('widget/add.html.twig', [
                    'form' => $form->createView()
                ]);
            }
            $widget = $widgetManager->saveWidget($file, $widget);
            if ($widget->getId()) {
                $this->addFlash('success', 'widget updated successfully');
            } else {
                $this->addFlash('danger', 'widget creation failed');
            }

            return $this->redirectToRoute('widget_admin_list');
        }
        return $this->render('widget/add.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/{id}/delete', name: 'delete', methods: ["POST"])]
    public function delete(Request $request, Widget $widget, WidgetManager $widgetManager, TranslatorInterface $translator): Response
    {
        if ($this->isCsrfTokenValid('delete-widget', $request->request->get('_token'))) {

            if ($widgetManager->removeWidget($widget)) {
                $this->addFlash('success', $translator->trans('widget_flash_delete_success', ['%widgetName%' => $widget->getName()]));
            } else {
                $this->addFlash('danger', $translator->trans('widget_flash_delete_failed', ['%widgetName%' => $widget->getName()]));
            }
        }
        return $this->redirectToRoute('widget_admin_list', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/edit', name: 'edit', methods: ['GET', 'POST'])]
    public function edit(Widget $widget, Request $request, WidgetManager $widgetManager): Response
    {
        $form = $this->createForm(WidgetFormType::class, $widget, ['required_file_input' => false]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();
            $errors = [];
            // validate file only if it is uploaded
            if (null !== $file) {
                $errors = $widgetManager->isValidWidgetFile($file);
            }
            if (!empty($errors)) {
                // Handle validation errors
                foreach ($errors as $error) {
                    $this->addFlash('danger', $error);
                }
                return $this->render('widget/edit.html.twig', [
                    'form' => $form->createView(),
                    'widget' => $widget
                ]);
            }

            $widgetManager->editWidget($file, $widget);
            $this->addFlash('success', 'widget updated successfull');
            return $this->redirectToRoute('widget_admin_list', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('widget/edit.html.twig', [
            'form' => $form->createView(),
            'widget' => $widget
        ]);
    }

    #[Route('/make_package/{id}', name: 'make_package', methods: ['POST'])]
    public function makePackage(Request $request, Widget $widget, WidgetManager $widgetManager): Response
    {
        $csrfToken = $request->request->get('_csrf_token');

        if (!$this->isCsrfTokenValid('widget_admin_make_package', $csrfToken)) {
            throw $this->createAccessDeniedException('Invalid CSRF token.');
        }

        $packageData = $widgetManager->makePackage($widget);

        $compressedContent = gzencode($packageData);

        $filename = $widget->getName() . '.json.gz';

        $response = new Response();
        $response->headers->set('Content-Type', 'application/zip');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->setContent($compressedContent);
        $response->setStatusCode(200);

        return $response;
    }

    #[Route('/import', name: 'import', methods: ['POST'])]
    public function importWidget(Request $request, WidgetManager $widgetManager): Response
    {
        $csrfToken = $request->request->get('_csrf_token');

        if (!$this->isCsrfTokenValid('import', $csrfToken)) {
            throw $this->createAccessDeniedException('Invalid CSRF token.');
        }

        $file = $request->files->get('file');

        if ($file && $file->isValid()) {
            try {
                $extension = $file->getClientOriginalExtension();
                if ($extension === 'gz') {
                    $content = gzdecode(file_get_contents($file->getPathname()));
                } else {
                    $content = file_get_contents($file->getPathname());
                }
                [$status, $message] = $widgetManager->importWidget($content);
            } catch (Exception $e) {
                [$status, $message] = [False, "Invalid file"];
            } finally {
                if ($status) {
                    $this->addFlash('success', $message);
                } else {
                    $this->addFlash('danger', $message);
                }
            }
        } else {
            $this->addFlash('danger', 'Invalid file uploaded.');
        }
        return $this->redirectToRoute('widget_admin_list', [], Response::HTTP_CREATED);
    }

    #[Route(path: '/release', name: 'release')]
    public function publishMultiple(Request $request, WidgetManager $widgetManager, #[CurrentUser] User $user): Response
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $form = $this->createForm(WidgetPublishFormType::class, null, ['site' => $site, 'profile' => $profile]);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid() && !$request->isXmlHttpRequest()) {
            $data = $form->getData();
            if ($profile->isSuperAdministrator()) {
                $brandList = isset($data['brandList']) ? $data['brandList'] : [];
            } else {
                $brand = $profile->isBrandAdministrator() ? $profile->getBrand()->getName() : $site->getBrand()->getName();
                $brandList = [$brand];
            }
            if (!$profile->isCountryUser()) {
                $countryList = isset($data['countryList']) ? $data['countryList'] : [];
            } else {
                $countryList = [$site->getCountry()->getName()];
            }
         
            $source = isset($data['source']) ? $data['source'] : [];
            $widgetList = isset($data['widgetList']) ? $data['widgetList'] : [];

            if (!empty($brandList) || !empty($countryList) || !empty($widgetList) || !empty($source)) {
                $response = $widgetManager->releaseMultiple($brandList, $countryList, $widgetList, $source);
                if ($response->getData() && $response->getCode() == Response::HTTP_OK) {
                    $this->addFlash('success', 'Successfully updated');
                } else {
                    $this->addFlash('danger', 'failed in synchronization');
                }
                return $this->redirectToRoute('widget_admin_list');
            } else {
                $this->addFlash('danger', 'Please fill in at least one parameter');
            }
        }
        return $this->render('widget/publish.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile
        ]);
    }
}
