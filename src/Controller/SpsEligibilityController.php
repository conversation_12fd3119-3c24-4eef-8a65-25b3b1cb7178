<?php

namespace App\Controller;

use App\Document\SpsEligibilityLcdv;
use App\Form\SpsEligibilityLcdvType;
use App\Service\SpsEligibilityLcdvService;
use App\Security\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route('/admin/sps-eligibility-lcdv', name: 'sps_eligibility_lcdv_')]
class SpsEligibilityController extends AbstractController
{
    /**
     * Display list of SPS Eligibility LCDV rules (Table 1 - Global page)
     */
    #[Route('', name: 'index', methods: ['GET'])]
    public function index(
        #[CurrentUser] User $user,
        SpsEligibilityLcdvService $service,
        Request $request
    ): Response {
        $profile = $user->getProfile();

        // Check if user has access to global pages (Super Admin, Central Admins, Brand Admins)
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isBrandAdministrator()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        // Get search parameters
        $lcdvCode = $request->query->get('lcdv_code');
        $type = $request->query->get('type');
        $disclaimer = $request->query->get('disclaimer');

        if ($disclaimer === 'true') {
            $disclaimer = true;
        } elseif ($disclaimer === 'false') {
            $disclaimer = false;
        } else {
            $disclaimer = null;
        }

        // Search or get all eligibility rules
        if ($lcdvCode || $type || $disclaimer !== null) {
            $eligibilityRules = $service->search($lcdvCode, $type, $disclaimer);
        } else {
            $eligibilityRules = $service->findAll();
        }

        // Get distinct types for filter dropdown
        $types = $service->getDistinctTypes();

        // Check if user can write (only super admins for global pages)
        $canWrite = $profile->isSuperAdmin();

        return $this->render('sps_eligibility_lcdv/index.html.twig', [
            'eligibility_rules' => $eligibilityRules,
            'types' => $types,
            'can_write' => $canWrite,
            'profile' => $profile,
            'search' => [
                'lcdv_code' => $lcdvCode,
                'type' => $type,
                'disclaimer' => $request->query->get('disclaimer')
            ]
        ]);
    }

    /**
     * Create new SPS Eligibility LCDV rule (Only for super admins)
     */
    #[Route('/new', name: 'new', methods: ['GET', 'POST'])]
    public function new(
        #[CurrentUser] User $user,
        Request $request,
        SpsEligibilityLcdvService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only super admins can create global SPS Eligibility rules
        if (!$profile->isSuperAdmin()) {
            throw $this->createAccessDeniedException('Only super administrators can create SPS Eligibility rules');
        }

        $spsEligibility = new SpsEligibilityLcdv();
        $form = $this->createForm(SpsEligibilityLcdvType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $response = $service->save($spsEligibility);

            if ($response->getCode() === 201) {
                $this->addFlash('success', $translator->trans('sps_eligibility_created_successfully'));
                return $this->redirectToRoute('sps_eligibility_lcdv_index');
            } else {
                $this->addFlash('error', $translator->trans('sps_eligibility_create_error'));
            }
        }

        return $this->render('sps_eligibility_lcdv/new.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile
        ]);
    }

    /**
     * Show SPS Eligibility LCDV rule details
     */
    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(
        #[CurrentUser] User $user,
        string $id,
        SpsEligibilityLcdvService $service
    ): Response {
        $profile = $user->getProfile();

        // Check if user has access to global pages (Super Admin, Central Admins, Brand Admins)
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isBrandAdministrator()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        $canWrite = $profile->isSuperAdmin();

        return $this->render('sps_eligibility_lcdv/show.html.twig', [
            'sps_eligibility' => $spsEligibility,
            'can_write' => $canWrite,
            'profile' => $profile
        ]);
    }

    /**
     * Edit SPS Eligibility LCDV rule (Only for super admins)
     */
    #[Route('/{id}/edit', name: 'edit', methods: ['GET', 'POST'])]
    public function edit(
        #[CurrentUser] User $user,
        Request $request,
        string $id,
        SpsEligibilityLcdvService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only super admins can edit global SPS Eligibility rules
        if (!$profile->isSuperAdmin()) {
            throw $this->createAccessDeniedException('Only super administrators can edit SPS Eligibility rules');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        $form = $this->createForm(SpsEligibilityLcdvType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $response = $service->save($spsEligibility);

            if ($response->getCode() === 200) {
                $this->addFlash('success', $translator->trans('sps_eligibility_updated_successfully'));
                return $this->redirectToRoute('sps_eligibility_lcdv_index');
            } else {
                $this->addFlash('error', $translator->trans('sps_eligibility_update_error'));
            }
        }

        return $this->render('sps_eligibility_lcdv/edit.html.twig', [
            'form' => $form->createView(),
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Delete SPS Eligibility LCDV rule (Only for super admins)
     */
    #[Route('/{id}/delete', name: 'delete', methods: ['POST'])]
    public function delete(
        #[CurrentUser] User $user,
        Request $request,
        string $id,
        SpsEligibilityLcdvService $service,
        TranslatorInterface $translator
    ): RedirectResponse {
        $profile = $user->getProfile();

        // Only super admins can delete global SPS Eligibility rules
        if (!$profile->isSuperAdmin()) {
            throw $this->createAccessDeniedException('Only super administrators can delete SPS Eligibility rules');
        }

        // Validate CSRF token
        $submittedToken = $request->request->get('token');
        if (!$this->isCsrfTokenValid('delete-sps-eligibility', $submittedToken)) {
            $this->addFlash('error', $translator->trans('invalid_csrf_token'));
            return $this->redirectToRoute('sps_eligibility_lcdv_index');
        }

        $response = $service->delete($id);

        if ($response->getCode() === 200) {
            $this->addFlash('success', $translator->trans('sps_eligibility_deleted_successfully'));
        } else {
            $this->addFlash('error', $translator->trans('sps_eligibility_delete_error'));
        }

        return $this->redirectToRoute('sps_eligibility_lcdv_index');
    }
}
