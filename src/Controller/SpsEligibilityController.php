<?php

namespace App\Controller;

use App\Entity\SpsEligibility;
use App\Form\SpsEligibilityType;
use App\Repository\SpsEligibilityRepository;
use App\Security\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route('/admin/sps-eligibility', name: 'sps_eligibility_')]
class SpsEligibilityController extends AbstractController
{
    /**
     * Display list of SPS Eligibility rules (Global page - read-only for brands, write access for product managers)
     */
    #[Route('', name: 'index', methods: ['GET'])]
    public function index(
        #[CurrentUser] User $user,
        SpsEligibilityRepository $repository,
        Request $request
    ): Response {
        $profile = $user->getProfile();
        
        // Check if user has access to global pages (Super Admin, Central Admins, Brand Admins)
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isBrandAdministrator()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        // Get search parameters
        $lcdvCode = $request->query->get('lcdv_code');
        $type = $request->query->get('type');
        $disclaimer = $request->query->get('disclaimer');
        
        if ($disclaimer === 'true') {
            $disclaimer = true;
        } elseif ($disclaimer === 'false') {
            $disclaimer = false;
        } else {
            $disclaimer = null;
        }

        // Search or get all eligibility rules
        if ($lcdvCode || $type || $disclaimer !== null) {
            $eligibilityRules = $repository->search($lcdvCode, $type, $disclaimer);
        } else {
            $eligibilityRules = $repository->findAllOrdered();
        }

        // Get distinct types for filter dropdown
        $types = $repository->getDistinctTypes();

        // Check if user can write (only super admins for global pages)
        $canWrite = $profile->isSuperAdmin();

        return $this->render('sps_eligibility/index.html.twig', [
            'eligibility_rules' => $eligibilityRules,
            'types' => $types,
            'can_write' => $canWrite,
            'profile' => $profile,
            'search' => [
                'lcdv_code' => $lcdvCode,
                'type' => $type,
                'disclaimer' => $request->query->get('disclaimer')
            ]
        ]);
    }

    /**
     * Create new SPS Eligibility rule (Only for product managers and super admins)
     */
    #[Route('/new', name: 'new', methods: ['GET', 'POST'])]
    public function new(
        #[CurrentUser] User $user,
        Request $request,
        EntityManagerInterface $entityManager,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();
        
        // Only super admins can create global SPS Eligibility rules
        if (!$profile->isSuperAdmin()) {
            throw $this->createAccessDeniedException('Only super administrators can create SPS Eligibility rules');
        }

        $spsEligibility = new SpsEligibility();
        $form = $this->createForm(SpsEligibilityType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($spsEligibility);
            $entityManager->flush();

            $this->addFlash('success', $translator->trans('sps_eligibility_created_successfully'));

            return $this->redirectToRoute('sps_eligibility_index');
        }

        return $this->render('sps_eligibility/new.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile
        ]);
    }

    /**
     * Show SPS Eligibility rule details
     */
    #[Route('/{id}', name: 'show', methods: ['GET'], requirements: ['id' => '\d+'])]
    public function show(
        #[CurrentUser] User $user,
        SpsEligibility $spsEligibility
    ): Response {
        $profile = $user->getProfile();
        
        // Check if user has access to global pages (Super Admin, Central Admins, Brand Admins)
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isBrandAdministrator()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        $canWrite = $profile->isSuperAdmin();

        return $this->render('sps_eligibility/show.html.twig', [
            'sps_eligibility' => $spsEligibility,
            'can_write' => $canWrite,
            'profile' => $profile
        ]);
    }

    /**
     * Edit SPS Eligibility rule (Only for product managers and super admins)
     */
    #[Route('/{id}/edit', name: 'edit', methods: ['GET', 'POST'], requirements: ['id' => '\d+'])]
    public function edit(
        #[CurrentUser] User $user,
        Request $request,
        SpsEligibility $spsEligibility,
        EntityManagerInterface $entityManager,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only super admins can edit global SPS Eligibility rules
        if (!$profile->isSuperAdmin()) {
            throw $this->createAccessDeniedException('Only super administrators can edit SPS Eligibility rules');
        }

        $form = $this->createForm(SpsEligibilityType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', $translator->trans('sps_eligibility_updated_successfully'));

            return $this->redirectToRoute('sps_eligibility_index');
        }

        return $this->render('sps_eligibility/edit.html.twig', [
            'form' => $form->createView(),
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Delete SPS Eligibility rule (Only for product managers and super admins)
     */
    #[Route('/{id}/delete', name: 'delete', methods: ['POST'], requirements: ['id' => '\d+'])]
    public function delete(
        #[CurrentUser] User $user,
        Request $request,
        SpsEligibility $spsEligibility,
        EntityManagerInterface $entityManager,
        TranslatorInterface $translator
    ): RedirectResponse {
        $profile = $user->getProfile();

        // Only super admins can delete global SPS Eligibility rules
        if (!$profile->isSuperAdmin()) {
            throw $this->createAccessDeniedException('Only super administrators can delete SPS Eligibility rules');
        }

        // Validate CSRF token
        $submittedToken = $request->request->get('token');
        if (!$this->isCsrfTokenValid('delete-sps-eligibility', $submittedToken)) {
            $this->addFlash('error', $translator->trans('invalid_csrf_token'));
            return $this->redirectToRoute('sps_eligibility_index');
        }

        try {
            $entityManager->remove($spsEligibility);
            $entityManager->flush();
            $this->addFlash('success', $translator->trans('sps_eligibility_deleted_successfully'));
        } catch (\Throwable $e) {
            $this->addFlash('error', $translator->trans('sps_eligibility_delete_error'));
        }

        return $this->redirectToRoute('sps_eligibility_index');
    }
}
