<?php

namespace App\Controller\Api;

use App\DataEntities\BrandEntities;
use App\Service\VehicleLabelService;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\DataTransformers\EvRoutingTransformer;
use App\DataTransformers\EvRoutingWithLcdvsTransformer;
use App\Helpers\BrandHelper;
use App\Repository\LcdvEvRoutingRepository;
use App\Service\EvRoutingService;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/v1')]
#[OA\Tag(name: "Ev Routing Config")]
class EvRoutingApiController extends AbstractController
{
    #[Route('/ev_routing', name: 'ev_routing', methods: ['GET'])]
    #[OA\Get(
        path: '/v1/ev_routing',
        summary: 'Get Ev Routing configuration',
        parameters: [
            new OA\Parameter(name: 'lcdv', in: 'query', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'dvq', in: 'query', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'b0f', in: 'query', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'dar', in: 'query', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'brand', in: 'query', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'country', in: 'query', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'language', in: 'query', required: false, schema: new OA\Schema(type: 'string')),
        ],
        responses: [
            new OA\Response(response: 200, description: "Successful Ev Routing fetch"),
            new OA\Response(response: 404, description: "Ev Routing config not found"),
            new OA\Response(response: 422, description: "Validation failed"),
        ]
    )]
    public function index(
        Request $request,
        ValidatorInterface $validator,
        LcdvEvRoutingRepository $repo,
        VehicleLabelService $service
    ): JsonResponse {
        $lcdv = mb_strtoupper($request->query->get('lcdv', ''));
        $dvq = mb_strtoupper($request->query->get('dvq', ''));
        $b0f = mb_strtoupper($request->query->get('b0f', ''));
        $dar = mb_strtoupper($request->query->get('dar', ''));
        $country = $request->query->get('country');
        $language = $request->query->get('language');
        $brand = $request->query->get('brand');

        $errors = $validator->validate(compact('lcdv', 'country', 'language', 'brand', 'dvq', 'b0f', 'dar'), new Assert\Collection([
            'brand' => BrandHelper::getConstraints(),
            'country' => CountryValidator::getConstraintsForCountry(),
            'language' => LanguageValidator::getConstraintsForLanguage(),
            'lcdv' => [
                new Assert\NotBlank(message: 'LCDV cannot be blank'),
                new Assert\Type(['type' => 'alnum', 'message' => 'LCDV must contain only alphanumeric characters']),
            ],
            'dvq' => [
                new Assert\NotBlank(message: 'DVQ cannot be blank'),
            ],
            'b0f' => [
                new Assert\NotBlank(message: 'B0F cannot be blank'),
            ],
            'dar' => [
                new Assert\NotBlank(message: 'DAR cannot be blank'),
            ],
        ]));

        $messages = static::getValidationMessages($errors);

        if (!empty($messages)) {
            return $this->getValidationErrorResponse($messages);
        }

        $lcdvEvRouting = $repo->getEvRoutingConfig($lcdv, $dvq, $b0f, $dar)->first();

        if ($lcdvEvRouting === false) {
            return $this->notFoundResponse('The Ev Routing config does not exist!');
        }

        $evRouting = $lcdvEvRouting->getEvRouting();

        return $this->successResponse(
            EvRoutingTransformer::make()->transform($evRouting),
            'EV Routing configuration retrieved successfully'
        );
    }

    #[Route('/ev_routings', name: 'ev_routings', methods: ['GET'])]
    #[OA\Get(
        path: '/v1/ev_routings',
        summary: 'Get all Ev Routings',
        parameters: [
            new OA\Parameter(name: 'brand', in: 'query', description: 'Brand', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'country', in: 'query', description: 'Country', required: true, schema: new OA\Schema(type: 'string')),
            new OA\Parameter(name: 'language', in: 'query', description: 'Language', required: true, schema: new OA\Schema(type: 'string')),
        ],
        responses: [
            new OA\Response(response: 200, description: "Successful Ev Routings fetch"),
            new OA\Response(response: 404, description: "No Ev Routings found for this brand"),
            new OA\Response(response: 422, description: "Validation failed"),
        ],
        tags: ['Ev Routing Config']
    )]
    public function getAllVehiclesByBrand(
        Request $request,
        EvRoutingService $service,
        ValidatorInterface $validator
    ): JsonResponse {
        $brand = $request->query->get('brand');
        $country = $request->query->get('country');
        $language = $request->query->get('language');

        $errors = $validator->validate(compact('brand', 'country', 'language'), new Assert\Collection([
            'brand' => BrandHelper::getConstraints(),
            'country' => CountryValidator::getConstraintsForCountry(),
            'language' => LanguageValidator::getConstraintsForLanguage(),
        ]));

        $messages = static::getValidationMessages($errors);

        if (!empty($messages)) {
            return static::getValidationErrorResponse($messages);
        }

        $evRoutings = $service->normalizeApiData($brand);

        if (empty($evRoutings)) {
            return $this->notFoundResponse('No EV routings found for this brand');
        }

        $data = EvRoutingWithLcdvsTransformer::make()->collection($evRoutings);

        return $this->successResponse(
            $data,
            'EV Routing configurations retrieved successfully'
        );
    }

}
