<?php
namespace App\MessageHandler;

use App\Message\PublishWidgetMessage;
use App\Repository\WidgetDataRepository;
use App\Repository\SiteRepository;
use App\Repository\LanguageRepository;
use App\Entity\Site;
use App\Service\WidgetManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class PublishWidgetMessageHandler
{
    public function __construct(
        private WidgetDataRepository $widgetDataRepo,
        private SiteRepository $siteRepo,
        private LanguageRepository $languageRepo,
        private EntityManagerInterface $em,
        private WidgetManager $publisher
    ) {}

    public function __invoke(PublishWidgetMessage $message)
    {
        $widgetData = $this->widgetDataRepo->find($message->getWidgetDataId());
        if (!$widgetData) {
            return;
        }
        $brand = $widgetData->getBrand();
        $country = $widgetData->getCountry();
        $site = $this->siteRepo->findOneBy(['brand' => $brand, 'country' => $country]);
        if (!$site) {
            $language = $this->languageRepo->findOneBy(['code' => 'en']); 
            $site = new Site();
            $site->setBrand($brand);
            $site->setCountry($country);
            $site->setLabel($brand->getCode() . '_' . $country->getCode());
            $site->setPreferedLanguage($language);
            $site->addLanguage($language);
            $site->setTimezone('UTC');
            $this->em->persist($site);
            $this->em->flush();
        }
        $this->publisher->publish($widgetData, $site);
    }
}
