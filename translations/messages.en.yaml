page_title: Site Creation
site_general: General
label: Role Name
region: Region
country: Country
domtom: Display the country selection message for the French overseas departments
distance_section: Distance
distance_unit: Unit of distance
consumption_section: Consumption
consumption_unit: Consumption Unit
volume_section: Volume
volume_unit: Volume unit
cost_section: Cost
cost: Cost
site_languages: Languages
reference_language: Reference language
default_language: Default Language
brand: Brand
create_site: Create Site
update_site: Backup Site
edit_site: Edit Site
save: Save 
edit: Edit
new_site: New Site
currency_section: Currencies
currency: Complementary currencies
date_section: Date
date_format: Date format
hour_format: Hour format
timezone: Time Zone
ldap_code: LDAP Code
sites_list_table: Sites list
reversed_name_order: Name Order Firstname/Lastname
move_all_languages: Available languages 
remove_all_languages: Selected languages
move_all_currencies: Available currencies
remove_all_currencies: Selected currencies
reference_label: Reference label
reference_label_release: Reference Label Release
feature: Feature
features: Features
created_at: Created at
updated_at: Updated at
label_key: Label key
channel: Channel
label_actions: Actions
export_all_label: Export all keys
export_filter_label: Export with filter
site_name: Site name
brand_name: Brand Name
languages: Languages
brands: Brands
countries: Countries
channels: Channels

first_last_name: "First name + Last name"
last_first_name: "Last name + First name"
civilites_customer_at_active: Customer@ civilities active
choose_country: Select a country
choose_brand: Select a brand
choose_profile: Select a profile
choose_language: Select a language
choose_channel: Select a channel
choose_status: select a status
choose_guid: select a GUID
choose_name: select a name
choose_type: select a type
choose_enabled: select a status
profile: Profile
admin_profiles: Admin profiles
sites_profiles: Site profiles

filter_place_holder: Filter

site_management: Site management
site_settings: National Settings
site_section_languages: Languages

# Buttons
btn_access: Access
btn_filter: Filter
btn_cancel: Cancel
btn_save: Save
btn_delete: Delete
btn_release: Release
btn_update: Update
btn_download: Download

# Sidebar Menu
search: Search
menu_admin: Administration
menu_features: Features
menu_social_network: Social Networks
menu_global_profiles: Global Profiles
menu_local_profiles: Local Profiles
menu_sites_list: Site Management
menu_sites_create: Site Creation
menu_translation_key: Reference Label Management
favorites: Favorites
menu_icon_management: Icon Management

#Flash message
success_site_create_message: Site saved successfully
success_site_edit_message: site updated successfully
success_translation_key_create_message: Translation key saved successfully
success_translation_key_edit_message: Translation key edited successfully
translation_key_exist: Translation Key Already Exist


msg_error_label_notblank: Site Name must not be blank


#Site page
user: User
logout: Logout
sign_in: Sign In
welcome_messge: Welcome to Space BO !
roles: Roles
groups: Groups
confirmMessageNoteSiteUpdate: Updating locale languages may affect local translations.
confirmMessageSiteUpdate: Are you sure you want to update this site?
confirm: Confirm
cancel: Cancel
confirmMessageSiteCreate: Are you sure you want to create this site?
confirmMessageNoteSiteCreation: You will create new release keys for the mentioned languages.


# Localization management
translation_key_new: Create new Translation key
translation_key_create: New Translation key
label_name: Label name
sprint_number: Sprint in which the label has been created
parameter_value: Parameter value
reference_translation_language: "%language% reference label"
translation_key_list_table: List of references labels
translation_key_management: Reference Label Management
translation_key_label: Reference Label Management
local_translation_list: Local Translations List
local_translation_management: Local Translations
translation_key_edit: Reference Label Management - Edit

#Translation Page
create_date: Create Date
update_date: Update Date
release_date: Release Date
device: Device
country_translation: Country Translation
actions: Actions

dashboard: Dashboard
media: Media Library
action: Action


#feature
road_assistance_management: Road Side Assistance Management
mop_integration: MOP Integration
maintenance_parameter: Maintenance parameters
term_of_use: Use conditions
customer_relationship: Customer Relationship
assistance_peugeot: Assistance Peugeot

add_image: Add File
edit_image: Edit File
add_folder: Add Folder
path: Path
add: Add
copy_url: Copy URL
folder: Folder
rename_folder: Rename Folder
list_images: Images List
list_media: Media List
zero_images: No matching images found
delete_confirmation: Are you sure you want to delete this folder?
delete_confirmation_image: Are you sure you want to delete this image?
confirmation: Confirmation
accepted_formats : Accepted Formats
accepted_media : Accepted Media
max_size: Max size accepted
text_alt: Alternative Text
comment : Comment
copyright: Copyright
name_exists: this name already exists

error_edit: Error while editing
error_delete: Error while deleting
error_add: Error while adding
folder_edited: Folder edited 
folder_added: Folder Added
folder_deleted: Folder Deleted
error_media_add: Error add media
error_media_edit: Error edit media
error_media_delete: Error delete media
media_added: Media added Successfully
media_edited: Media edited Successfully
media_deleted: Media deleted Successfully

# Role management
add_new_app_service: Add new Application Service
environment: Environment
success_edit_role_message: Role edited successfully
roles_list: Roles List
role_management: Role 
access_management: Access Management
app_service_code: Application Service Name
edit_role: Edit Role
role: Role
role_menus_edit_success: Role menus edited successfully
role_menu_management: Menu
edit_role_menus: Menu Options Access by Role
select_deny: Deny
select_write: Write
select_read: Read

# Profile management
profile_flash_success_update: Profile menu updated successfully

activation: Activation
telephone: Telephone
faq: FAQ
form_customer: Form Customer 
none: None
non-care: Non care
c1st: Custormer1st
apply_form_selection: Apply

# User login block
user-dashboard: Dashboard
user-preferences: User Preferences

#feature_setting
feature_setting: Feature Setting
title_user_preferences: User Preferences
title_settings: Settings
application_services: Application Services
close: Close
edit_translation_local: Edit Local Translations
feature_setting_level_error: the "level" field should not be empty
feature_setting_invalid_level_error: 'Invalid level: %level%'
feature_setting_invalid_role_error: Only the super admin can add new feature settings
feature_setting_invalid_json_error: There is something wrong with the uploaded file. Please upload a valid JSON file
feature_setting_invalid_type_error: 'Invalid type: %type%'

#widget
widget_management: Widget Management
save_widget_feature_success: data successfully saved
publish_confirmation_msg: Are you sure you want to publish this widget's settings ?
widget_administration: Widget Administration
widget_modal_title: Delete a Widget
widget_modal_body: Are you sure to delete this Widget ?
widget_flash_delete_success: The widget "%widgetName%" is successfully deleted !
widget_flash_delete_failed: Failed to delete The widget "%widgetName%" OR this widget has already some configurations !!
widget_edit: Widget - Edit
widget_add: Widget - Add
widget: Widget
name: Name
description: Description
version: Version
type: Type
widget_s3_open_json_button: Open JSON
widget_s3_language: Language
widget_s3_file: File
widget_s3_creation_date: Creation Date
widget_s3_last_update_date: Last Update Date
widget_s3_json_viewer_title: JSON Viewer
widget_s3_json_file_block_title: JSON File
regis_doc_image_select_header: Image selection
ajouter: Add
supprimer: DELETE
view: View
view_current_config: View current config

# Errors pages
error_page_previous_message: You can go back to
error_page_previous_btn: Previous Page
error_page_404_title: Page not found
error_page_404_description: The page you are looking for does not exist.
error_page_403_title: Access denied
error_page_403_description: Sorry about that, but you don't have permission to access this page.

local_translation: Local Translation
reference_translation: Reference Translation
labels: Labels

# local translations csv exports
export_ltranslations_root_filename: Local_Translations
export_rtranslations_root_filename: Reference_Translations
export_ltranslations_language: Language
export_ltranslations_culture: Culture
export_ltranslations_translationKey: Key
export_ltranslations_operation: Operation
export_ltranslations_createdDate: Creation Date
export_ltranslations_updatedDate: Update Date
export_ltranslations_releaseDate: Release Date
export_ltranslations_sprint: Sprint
export_ltranslations_parameterValue: Parameter Value
export_ltranslations_device: Source
export_ltranslations_feature: Feature
export_ltranslations_widget: Widget 
export_ltranslations_brand: Brand
export_ltranslations_label: Translation
export_ltranslations_labelToUpdate: Label to Update
export_ltranslations_operation_toCreate: toCreate
export_ltranslations_operation_toUpdate: toUpdate
export_ltranslations_referenceLabel: Reference Label

# local translations csv imports form
label_translation_csv_import_form: Import from CSV file
label_translation_csv_import_rows_to_update: Labels that will be created or updated
label_translation_csv_import_rows_with_errors: Labels that should be updated or created but with validation errors 
label_translation_csv_import_errors_column: Validation errors
label_translation_csv_import_rows_to_skip: Labels that will be skipped
label_translation_csv_import_labels: labels

edit_features: Edit Features Setting
create_features: Create Features
add_application: Add Application
profile_list: Profile List
edit_profile: Edit Profile
edit_translation_key: Edit Translation Key
create_translation_key: Create Transalation Key
widgets: Widgets
edit_widget: Edit Widget
config: Config


# import/export box
import: Import
export: Export
upload: Upload
csv_box_comment: Upload a CSV file
export_denied_message: Export feature is available only for Super Admin
loader_import_in_progress: File import in progress...
loader_processing_in_progress: Processing in progress...

# reference translation dashboard
reference_translation_dashboard: Reference Translation Dashboard
reference_translation_dashboard_title: Reference Translation Dashboard
reference_translation_dashboard_language: Reference Language
reference_translation_dashboard_completion: Completion
reference_translation_dashboard_translated: Translated
reference_translation_dashboard_untranslated: Unstranslated
reference_translation_dashboard_total_keys: Total Reference Keys

# dashboard
dashboard_name: Dashboard Name
dashboard_actions: Actions
unavailable_dashboards: No Available Dashboards

# multi-fields
list_objects: List of Objects
copy: Copy
remove: Remove
preview: Preview
upload_file: Upload File
choose_file: Choose File

# local translation dashboard
local_translation_dashboard: Local Translation Dashboard
local_translation_dashboard_title: Local Translation Dashboard
local_translation_dashboard_language: Local Language
local_translation_dashboard_completion: Completion
local_translation_dashboard_translated: Translated
local_translation_dashboard_untranslated: Unstranslated
local_translation_dashboard_total_keys: Total Local Keys

#Icon Management
select_icon: Select Icon
select_menu: Select Menu
success_icon_message: Icon has been saved successfully

#Features Menus
global_features: Global Features
brand_features: Brand Features
local_features: Local Features

#Massive Operation
short_languages: Lang
total_translation: Total 
set_translation: Set
unset_translation: Unset

#Reference Label Release
local_languages: Local Languages
status: Status
filename: Filename
language: Language
updatedate: Updated Date
importedate: Imported Date
releasedate: Released Date

# Environment
dev: Development
develop: Development
preprod: Preprod
integ: Integration
prod: Production

#JsonViewer
currentConfig: Current Config
releasedConfig: Released Config
diffView: Diff View
releasedConfiguration: Released Configuration
currentConfiguration: Current Configuration

#Massive Operation
bulk_operation: Bulk Operation
localLanguage: Loc. Lang.
keyName: Key Name
translation: Translation 
referenceLanguage: Ref. Lang.
referenceTranslation: Ref. Translation
updatedAt: Updated
select_demo: Select Tag
localTranslation_tag_creation: Local Translation Tag Creation
summary_title: Translations Summary Table
translations_title: Translations Table
massive.btn.apply: Apply
massive.wizard.btn.next: Next
massive.wizard.btn.finish: Finish
massive.wizard.btn.previous: Previous
massive.wizard.step1.title: Bulk Copy Wizard Step 1 - Check the source translations
massive.wizard.step2.title: Bulk Copy Wizard Step 2 - Select the target translations
massive.wizard.step3.title: Bulk Copy Wizard Step 3 - Final result preview
massive.error.bulk_operation_no_valid_items_selected: No valid translations selected. None of the selected items contain a local translation. Please select at least one LOCAL translation.
massive.wizard.error.select_brands_or_countries: Please select at least one target brand or country.
massive.wizard.error.select_channels: Please select at least one target channel.
massive.info.final_report_dry_run: 'Bulk copy completed: %total% records will be processed, %inserted% will be inserted, %updated% will be updated, %unchanged% will remain unchanged'
massive.info.final_report: 'Bulk copy completed: %total% records processed, %inserted% inserted, %updated% updated'
massive.info.final_report_head: Bulk %operation% has been successfully completed.
massive.error.final_report: An error occurred while performing the Bulk Copy operation.

massive.wizard.delete.step1.title: Bulk Delete Wizard - Are you sure you want to delete these translations ?
massive.info.final_report.delete: 'Bulk Delete completed: %total% records has been deleted'
bulk_operation.Copy: Copy
bulk_operation.delete: Delete

#access log
access_log:
  menu: History Data Log
  title: History logs
  site: Site
  brand: Brand
  country: Country
  action: Action
  route: Route
  actions: Actions
  logDetail: Log Details
  role: Role
  idEntity: Entity ID
  routeIdentifier: Route identifier
  valueBefore: Value before
  valueAfter: Value after

  userManualHelp: This setting is usually administered by the global administrator. Only change it if necessary!
  Reference Label Release: Reference Label Release
  region: Region
  id: ID
  download: Download
  update: Update
  selected_role: Selected Role
  menus: Menus
  configuration: Configuration
  create: Create
  mode: Mode
  back_to_list: Back to list
  culture: Culture
  user_groups_and_roles: User Groups and Roles
  settings: Settings
  label_translation_bulk_operations: Label Translation Bulk Operations


error_media_validation: The file name is invalid
confirmation_delete_image: Are you sure you want to delete this file ?
copy_clipboard: Successfully Copied

access_control: Access Control
read: Read
delete_confirmation_feature_setting: Are you sure you want to delete this feature ?
delete_feature_setting_warning: This action is irreversible and will delete this feature for all levels
feature_setting_deleted: Feature deleted successfully
widget_configuration: Configuration
user_preferences_save: User Preferences saved successfully
file: File
translation_status: Translation Status
env: Environment
available: Available
selected: Selected
update_feature: Update
backlist: Back to list
site1: Site
bo_access: Bo access
role_type: Role Type
add_new: Add new
source: Source
settings_msg: Success settings synchronization
settings_failed_msg: Failed settings synchronization
new_widget: New Widget
feature_configuration: Feature Configuration
feature_save: Feature added successfully

# SPS Eligibility
SPS Eligibility: SPS Eligibility
Table 1 (LCDV): Table 1 (LCDV)
Table 2 (Model): Table 2 (Model)
LCDV: LCDV
Model: Model
LCDV Eligibility Rules: LCDV Eligibility Rules
Model Eligibility Rules: Model Eligibility Rules
Search Filters: Search Filters
All Types: All Types
All Models: All Models
All: All
Enter LCDV code...: Enter LCDV code...
e.g. 2020: e.g. 2020
Search: Search
Clear: Clear
View: View
Edit: Edit
Delete: Delete
Yes: Yes
No: No
Actions: Actions
No LCDV eligibility rules found.: No LCDV eligibility rules found.
No model eligibility rules found.: No model eligibility rules found.
Are you sure you want to delete this rule?: Are you sure you want to delete this rule?
SPS Eligibility - Table 1 (LCDV): SPS Eligibility - Table 1 (LCDV)
SPS Eligibility - Table 2 (Model): SPS Eligibility - Table 2 (Model)
Add New LCDV Rule: Add New LCDV Rule
Add New Model Rule: Add New Model Rule
LCDV Codes: LCDV Codes
Vehicle Models: Vehicle Models
Model Year From: Model Year From
Vehicles with models: Vehicles with models
and model year: and model year
or newer are eligible: or newer are eligible
SPS Eligibility LCDV Rule Details: SPS Eligibility LCDV Rule Details
SPS Eligibility Model Rule Details: SPS Eligibility Model Rule Details
Add New SPS Eligibility LCDV Rule: Add New SPS Eligibility LCDV Rule
Add New SPS Eligibility Model Rule: Add New SPS Eligibility Model Rule
Edit SPS Eligibility LCDV Rule: Edit SPS Eligibility LCDV Rule
Edit SPS Eligibility Model Rule: Edit SPS Eligibility Model Rule
Are you sure you want to delete this SPS Eligibility LCDV rule?: Are you sure you want to delete this SPS Eligibility LCDV rule?
Are you sure you want to delete this SPS Eligibility Model rule?: Are you sure you want to delete this SPS Eligibility Model rule?
No SPS Eligibility LCDV rules found: No SPS Eligibility LCDV rules found
No SPS Eligibility Model rules found: No SPS Eligibility Model rules found
sps_eligibility_created_successfully: SPS Eligibility rule created successfully
sps_eligibility_updated_successfully: SPS Eligibility rule updated successfully
sps_eligibility_deleted_successfully: SPS Eligibility rule deleted successfully
sps_eligibility_delete_error: Error occurred while deleting SPS Eligibility rule
sps_eligibility_lcdv_codes_not_blank: LCDV codes are required
sps_eligibility_rules_not_blank: Eligibility rules are required
sps_eligibility_type_not_blank: Type is required
invalid_csrf_token: Invalid security token
feature_update: Feature updated successfully
enabled: Enabled
release: Release
creationDate: creation Date
route1: Route
user_access_log: User
entity: Entity
settings_release: Settings Release

form:
    file_upload:
        label: Choose a file
        description: No file chosen
        browse: Browse

datatable:
  search: Search
  showing: Showing _START_ to _END_ of _TOTAL_ entries
  length_menu: Show _MENU_ entries
  no_records: No records available
  previous: Previous
  next: Next

distance_unit_labels:
    km: Kilometers
    miles: Miles
  
media_file: File
media_creation_date : Creation date
media_folder: Folder
media_title: Title
media_legende: Legende (alt)
media_copyright: Copyright
media_comment: Comment

error_media_existing_header: Confirmation Message
error_media_existing_title: Are you sure you want to erase those files ?
error_media_existing_cancel: Cancel
error_media_existing_confirm: Yes! I Confirm

file_no_error: There is no error, the file uploaded with success
file_exceeds_max_filesize_ini: The uploaded file exceeds the upload_max_filesize directive in php.ini
file_exceeds_max_filesize_html: The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form
file_partially_uploaded: The uploaded file was only partially uploaded
file_not_uploaded: No file was uploaded
file_missing_temporary_folder: Missing a temporary folder
file_field_to_write_in_disk: Failed to write file to disk.
file_php_extension_stopped_upload: A PHP extension stopped the file upload.

error_bulk_operation_no_valid_items_selected: No valid translations selected. None of the selected items contain a local translation. Please select at least one LOCAL translation.

#Channel Management
success_channel_create_message: Channel Created Successfully
channel_updated_successfull: Channel Updated Successfully
channel_management: Channel Management
channel_type_name: Channel Type 
channel_type_description: Channel Type Description
new_channel: New Channel

folder_name: Folder Name
delete_folder: Delete Folder
edit_folder: Edit Folder
detail_info_title: Information details
media_extension: File Extension
media_size: File Size
media_preview : Preview
media_download : Download
media_list_title: Files List
media_no_result_title: No results found
media_no_result_description: No results match the filter criteria. Clear filters to show results.
media_no_folder_selected_title: No folder selected
media_no_folder_selected_description: Please select folder
media_no_file_exist_title: No File exist
media_no_file_exist_description: Please add new file
media_tree_title: File Explorer
media_buttons_actions_add: Add
media_buttons_actions_edit: Edit
media_buttons_actions_delete: Delete
media_buttons_actions_copy: Copy URL
media_records: records
invalid_folder_name: Invalid Folder Name

choose_media: Choose Media
choose_image: Choose Image
#languages
Albanais: Albanian
Allemand: German
Anglais: English
Arabe: Arabic
Bulgare: Bulgarian
Chinois: Chinese
Coréen: Korean
Croate: Croatian
Danois: Danish
Espagnol: Spanish
Estonien: Estonian
Francais: French
Géorgien: Georgian
Grec: Greek
Hébreu: Hebrew
Hindi: Hindi
Hongrois: Hungarian
Indonesien: Indonesian
Irlandais: Irish
Islandais: Icelandic
Italien: Italian
Japonais: Japanese
Lao: Lao
Letton: Latvian
Lituanien: Lithuanian
Malgache: Malagasy
Neerlandais: Dutch
Norvegien: Norwegian
Polonais: Polish
Portugais: Portuguese
Roumain: Romanian
Russe: Russian
Slovaque: Slovak
Slovene: Slovenian
Suedois: Swedish
Tcheque: Czech
Thai: Thai
Turc: Turkish
Ukrainien: Ukrainian
Vietnamien: Vietnamese
Wallon: Walloon

access_logs: Access logs
widget_information: Widget Information
widget_data_information: Widget Data Information
username: Username
featureName: Feature Name
attributeNames: Changed Attributes
before: Before
after: After
date: Date

vehicle_model_label: Vehicle Model Label
vehicleModelLabel: Label
create : Create Model
search_label: Label
search_brand: Brand
lcdv_code: LCDV Code
sdp: SDP
updateAt: Updated At
defaultImage: Default Image
chooseImage: Choose Image
saveModel: Save Model
addLcdv: Add 

invalid_form: Please fill in all required fields
invalid_label: Please provide a label
invalid_lcvd_msg: You must add at least one LCDV code.
saveImage: Media Library
back: Back
invalid_image: Please select a image

# Ev Routing
ev_routing: Ev Routing
EV_ROUTINGS: Ev Routing
ev_routing_new: Add
ev_routing_update: Update
ev_routing_delete: Are you sure you want to delete this setting ?
ev_routing_label: label
ev_routing_lcdv: LCDV
ev_routing_rpo: RPO
ev_routing_engine_type: Engine TYPE
ev_routing_csciph: Constant speed consumption Kwh/100Km
ev_routing_max_charging: Max charging 
ev_routing_max_speed: Max speed
ev_routing_weight: weight
ev_routing_axle_weight: Axle weight
ev_routing_length: length
ev_routing_width: Width
ev_routing_height: Height
ev_routing_acceleration_efficiency: Acceleration efficiency
ev_routing_deceleration_efficiency: Deceleration efficiency
ev_routing_uphill_efficiency: UpHill efficiency
ev_routing_downhill_efficiency: DownHill efficiency
ev_routing_charging_curve_array: Curve array
ev_routing_planner : Ev Routing Planner
ev_routing_planner_menu : Ev Routing Planner
ev_routing_configuration_updated : Ev Routing Updated Successfully

# Additional SPS Eligibility Translations
sps_eligibility: SPS Eligibility
Disclaimer: Disclaimer
Enter model name...: Enter model name...
SPS Eligibility LCDV rule created successfully: SPS Eligibility LCDV rule created successfully
SPS Eligibility LCDV rule updated successfully: SPS Eligibility LCDV rule updated successfully
SPS Eligibility Model rule created successfully: SPS Eligibility Model rule created successfully
SPS Eligibility Model rule updated successfully: SPS Eligibility Model rule updated successfully
Error creating SPS Eligibility LCDV rule: Error creating SPS Eligibility LCDV rule
Error updating SPS Eligibility LCDV rule: Error updating SPS Eligibility LCDV rule
Error creating SPS Eligibility Model rule: Error creating SPS Eligibility Model rule
Error updating SPS Eligibility Model rule: Error updating SPS Eligibility Model rule

# SPS Eligibility Form Labels and Messages
Create New LCDV Eligibility Rule: Create New LCDV Eligibility Rule
Create New Model Eligibility Rule: Create New Model Eligibility Rule
Edit LCDV Eligibility Rule: Edit LCDV Eligibility Rule
Edit Model Eligibility Rule: Edit Model Eligibility Rule
LCDV Codes (up to 20 characters each): LCDV Codes (up to 20 characters each)
"Enter LCDV codes separated by commas (e.g., ABC123, XYZ789, LONG1234567890)": "Enter LCDV codes separated by commas (e.g., ABC123, XYZ789, LONG1234567890)"
Enter LCDV codes separated by commas...: Enter LCDV codes separated by commas...
Up to 20 characters per code: Up to 20 characters per code
Letters and numbers allowed: Letters and numbers allowed
Separate multiple codes with commas: Separate multiple codes with commas
"Example: ABC123, XYZ789, LONG1234567890": "Example: ABC123, XYZ789, LONG1234567890"
LCDV Eligibility Rule Details: LCDV Eligibility Rule Details
Model Eligibility Rule Details: Model Eligibility Rule Details
Create LCDV Rule: Create LCDV Rule
Create Model Rule: Create Model Rule
Update LCDV Rule: Update LCDV Rule
Update Model Rule: Update Model Rule
Help Information: Help Information
Model Information: Model Information
Eligibility Types: Eligibility Types
Standard: Standard
Premium: Premium
Special: Special
Custom: Custom
Basic eligibility rules: Basic eligibility rules
Enhanced service eligibility: Enhanced service eligibility
Special case eligibility: Special case eligibility
Custom eligibility rules: Custom eligibility rules
Enter the vehicle model name: Enter the vehicle model name
Use standard model naming conventions: Use standard model naming conventions
Model year defines minimum eligibility year: Model year defines minimum eligibility year
"Example: Corsa, Astra, Insignia": "Example: Corsa, Astra, Insignia"
Rule Information: Rule Information
Current LCDV Codes: Current LCDV Codes
Current Model Details: Current Model Details
Rule Details: Rule Details
Disclaimer Required: Disclaimer Required
Created: Created
Last Updated: Last Updated
Are you sure you want to delete this LCDV rule?: Are you sure you want to delete this LCDV rule?
Are you sure you want to delete this Model rule?: Are you sure you want to delete this Model rule?
New Rule: New Rule
Edit Rule: Edit Rule
LCDV Rules: LCDV Rules
Model Rules: Model Rules