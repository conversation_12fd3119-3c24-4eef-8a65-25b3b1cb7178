<?php

namespace App\Tests\Functional\Api;

use App\Entity\EvRouting;
use App\Entity\LcdvEvRouting;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class EvRoutingApiTest extends WebTestCase
{
    private $client;
    private $entityManager;

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
        
        // Clear the database and set up test data
        $this->setupTestData();
    }

    /**
     * Set up test data for EV routing tests
     */
    private function setupTestData(): void
    {
        // Create test EV routing
        $evRouting = new EvRouting();
        $evRouting->setEnabled(true);
        $evRouting->setLabel('Test EV Routing');
        $evRouting->setBrand('DS');
        $evRouting->setConstantSpeedConsumptionInkWhPerHundredkm('20.5');
        $evRouting->setEngineType('electric');
        $evRouting->setMaxChargeInkWh('75');
        $evRouting->setVehicleMaxSpeed('180');
        $evRouting->setVehicleWeight('2000');
        $evRouting->setVehicleAxleWeight('1000');
        $evRouting->setVehicleLength('4.5');
        $evRouting->setVehicleWidth('1.8');
        $evRouting->setVehicleHeight('1.6');
        $evRouting->setAccelerationEfficiency('0.8');
        $evRouting->setDecelerationEfficiency('0.7');
        $evRouting->setUphillEfficiency('0.75');
        $evRouting->setDownhillEfficiency('0.85');
        $evRouting->setChargingCurveArray('[10,20,30,40,50]');
        $evRouting->setDvq('DVQ123');
        $evRouting->setB0f('B0F123');
        $evRouting->setDar('DAR123');
        
        $this->entityManager->persist($evRouting);
        
        // Create LCDV EV routing association
        $lcdvEvRouting = new LcdvEvRouting();
        $lcdvEvRouting->setLcdv('ABC123');
        $lcdvEvRouting->setEvRouting($evRouting);
        
        $this->entityManager->persist($lcdvEvRouting);
        $this->entityManager->flush();
    }

    /**
     * Test getting a specific EV routing configuration
     */
    public function testGetEvRoutingConfiguration(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routing',
            [
                'lcdv' => 'ABC123',
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertTrue($responseData['enabled']);
        $this->assertEquals('Test EV Routing', $responseData['label']);
        $this->assertEquals('20.5', $responseData['constantSpeedConsumptionInkWhPerHundredkm']);
        $this->assertEquals('electric', $responseData['engineType']);
        $this->assertEquals('75', $responseData['maxChargeInkWh']);
    }

    /**
     * Test getting a non-existent EV routing configuration
     */
    public function testGetNonExistentEvRoutingConfiguration(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routing',
            [
                'lcdv' => 'NONEXISTENT',
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals('The Ev Routing config does not exist!', $responseData['message']);
    }

    /**
     * Test validation errors when getting EV routing configuration
     */
    public function testEvRoutingConfigurationValidationErrors(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routing',
            [
                'lcdv' => '', // Empty LCDV should fail validation
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertArrayHasKey('errors', $responseData);
        $this->assertArrayHasKey('lcdv', $responseData['errors']);
    }

    /**
     * Test getting all EV routings for a brand
     */
    public function testGetAllEvRoutingsForBrand(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routings',
            [
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertIsArray($responseData);
        $this->assertGreaterThanOrEqual(1, count($responseData));
        $this->assertEquals('Test EV Routing', $responseData[0]['Label']);
    }

    /**
     * Test validation errors when getting all EV routings
     */
    public function testGetAllEvRoutingsValidationErrors(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routings',
            [
                'brand' => '', // Empty brand should fail validation
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertEquals('validation_failed', $responseData['success']);
        $this->assertArrayHasKey('messages', $responseData);
        $this->assertArrayHasKey('brand', $responseData['messages']);
    }

    /**
     * Test missing required parameters when getting EV routing configuration
     */
    public function testMissingRequiredParametersForEvRouting(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routing',
            [
                // Missing lcdv, dvq, b0f, dar parameters
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertArrayHasKey('errors', $responseData);
        // Check for missing parameter errors
        $this->assertArrayHasKey('lcdv', $responseData['errors']);
        $this->assertArrayHasKey('dvq', $responseData['errors']);
        $this->assertArrayHasKey('b0f', $responseData['errors']);
        $this->assertArrayHasKey('dar', $responseData['errors']);
    }

    /**
     * Test invalid country code when getting EV routing configuration
     */
    public function testInvalidCountryCodeForEvRouting(): void
    {
        $this->client->request(
            'GET',
            '/v1/ev_routing',
            [
                'lcdv' => 'ABC123',
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'INVALID', // Invalid country code
                'language' => 'fr'
            ]
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $this->client->getResponse()->getStatusCode());
        
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        
        $this->assertArrayHasKey('errors', $responseData);
        $this->assertArrayHasKey('country', $responseData['errors']);
    }

    protected function tearDown(): void
    {
        // Clean up test data
        $lcdvEvRoutingRepo = $this->entityManager->getRepository(LcdvEvRouting::class);
        $evRoutingRepo = $this->entityManager->getRepository(EvRouting::class);
        
        $lcdvEvRoutings = $lcdvEvRoutingRepo->findAll();
        foreach ($lcdvEvRoutings as $lcdvEvRouting) {
            $this->entityManager->remove($lcdvEvRouting);
        }
        
        $evRoutings = $evRoutingRepo->findAll();
        foreach ($evRoutings as $evRouting) {
            $this->entityManager->remove($evRouting);
        }
        
        $this->entityManager->flush();
        
        parent::tearDown();
    }
}
