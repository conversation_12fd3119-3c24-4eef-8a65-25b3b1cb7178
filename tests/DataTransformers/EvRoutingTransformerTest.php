<?php

namespace App\Tests\DataTransformers;

use App\DataTransformers\EvRoutingTransformer;
use App\Entity\EvRouting;
use PHPUnit\Framework\TestCase;

class EvRoutingTransformerTest extends TestCase
{
    private EvRoutingTransformer $transformer;

    protected function setUp(): void
    {
        $this->transformer = EvRoutingTransformer::make();
    }

    /**
     * Test transforming an EvRouting entity to an array
     */
    public function testTransform(): void
    {
        // Create mock EvRouting entity
        $evRouting = $this->createMock(EvRouting::class);
        $evRouting->method('isEnabled')->willReturn(true);
        $evRouting->method('getLabel')->willReturn('Test EV Routing');
        $evRouting->method('getBrand')->willReturn('DS');
        // Required fields in the entity but not used in the transformer
        $evRouting->method('getDvq')->willReturn('65');
        $evRouting->method('getB0f')->willReturn('ES');
        $evRouting->method('getDar')->willReturn('10');
        $evRouting->method('getConstantSpeedConsumptionInkWhPerHundredkm')->willReturn('20.5');
        $evRouting->method('getEngineType')->willReturn('electric');
        $evRouting->method('getMaxChargeInkWh')->willReturn('75');
        $evRouting->method('getVehicleMaxSpeed')->willReturn('180');
        $evRouting->method('getVehicleWeight')->willReturn('2000');
        $evRouting->method('getVehicleAxleWeight')->willReturn('1000');
        $evRouting->method('getVehicleLength')->willReturn('4.5');
        $evRouting->method('getVehicleWidth')->willReturn('1.8');
        $evRouting->method('getVehicleHeight')->willReturn('1.6');
        $evRouting->method('getAccelerationEfficiency')->willReturn('0.8');
        $evRouting->method('getDecelerationEfficiency')->willReturn('0.7');
        $evRouting->method('getUphillEfficiency')->willReturn('0.75');
        $evRouting->method('getDownhillEfficiency')->willReturn('0.85');
        $evRouting->method('getChargingCurveArray')->willReturn('{"chargingParameters":{"batteryCurve":[{"stateOfChargeInkWh":1.0,"maxPowerInkW":66.4}]}}');
        

        // Transform the entity
        /** @var EvRouting&\PHPUnit\Framework\MockObject\MockObject $evRouting */
        $result = $this->transformer->transform($evRouting);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertTrue($result['enabled']);
        $this->assertEquals('Test EV Routing', $result['label']);
        // Brand is not included in the transformer output
        $this->assertEquals('20.5', $result['constantSpeedConsumptionInkWhPerHundredkm']);
        $this->assertEquals('electric', $result['engineType']);
        $this->assertEquals('75', $result['maxChargeInkWh']);
        $this->assertEquals('180', $result['vehicleMaxSpeed']);
        $this->assertEquals('2000', $result['vehicleWeight']);
        $this->assertEquals('1000', $result['vehicleAxleWeight']);
        $this->assertEquals('4.5', $result['vehicleLength']);
        $this->assertEquals('1.8', $result['vehicleWidth']);
        $this->assertEquals('1.6', $result['vehicleHeight']);
        $this->assertEquals('0.8', $result['accelerationEfficiency']);
        $this->assertEquals('0.7', $result['decelerationEfficiency']);
        $this->assertEquals('0.75', $result['uphillEfficiency']);
        $this->assertEquals('0.85', $result['downhillEfficiency']);
        $this->assertEquals('{"chargingParameters":{"batteryCurve":[{"stateOfChargeInkWh":1.0,"maxPowerInkW":66.4}]}}', $result['chargingCurveArray']);
        // These fields are not in the transformer result
    }

    /**
     * Test transforming an EvRouting entity with null values
     */
    public function testTransformWithNullValues(): void
    {
        // Create mock EvRouting entity with some null values
        $evRouting = $this->createMock(EvRouting::class);
        $evRouting->method('isEnabled')->willReturn(true);
        $evRouting->method('getLabel')->willReturn('Test EV Routing');
        $evRouting->method('getBrand')->willReturn('DS');
        $evRouting->method('getDvq')->willReturn('65');
        $evRouting->method('getB0f')->willReturn('ES');
        $evRouting->method('getDar')->willReturn('10');
        $evRouting->method('getConstantSpeedConsumptionInkWhPerHundredkm')->willReturn('20.5');
        $evRouting->method('getEngineType')->willReturn('electric');
        $evRouting->method('getMaxChargeInkWh')->willReturn('75');
        $evRouting->method('getVehicleMaxSpeed')->willReturn(null);
        $evRouting->method('getVehicleWeight')->willReturn(null);
        $evRouting->method('getVehicleAxleWeight')->willReturn(null);
        $evRouting->method('getVehicleLength')->willReturn(null);
        $evRouting->method('getVehicleWidth')->willReturn(null);
        $evRouting->method('getVehicleHeight')->willReturn(null);
        $evRouting->method('getAccelerationEfficiency')->willReturn(null);
        $evRouting->method('getDecelerationEfficiency')->willReturn(null);
        $evRouting->method('getUphillEfficiency')->willReturn(null);
        $evRouting->method('getDownhillEfficiency')->willReturn(null);
        $evRouting->method('getChargingCurveArray')->willReturn(null);
        

        // Transform the entity
        /** @var EvRouting&\PHPUnit\Framework\MockObject\MockObject $evRouting */
        $result = $this->transformer->transform($evRouting);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertTrue($result['enabled']);
        $this->assertEquals('Test EV Routing', $result['label']);
        $this->assertEquals('20.5', $result['constantSpeedConsumptionInkWhPerHundredkm']);
        $this->assertEquals('electric', $result['engineType']);
        $this->assertEquals('75', $result['maxChargeInkWh']);
        $this->assertNull($result['vehicleMaxSpeed']);
        $this->assertNull($result['vehicleWeight']);
        $this->assertNull($result['vehicleAxleWeight']);
        $this->assertNull($result['vehicleLength']);
        $this->assertNull($result['vehicleWidth']);
        $this->assertNull($result['vehicleHeight']);
        $this->assertNull($result['accelerationEfficiency']);
        $this->assertNull($result['decelerationEfficiency']);
        $this->assertNull($result['uphillEfficiency']);
        $this->assertNull($result['downhillEfficiency']);
        $this->assertNull($result['chargingCurveArray']);
    }

    /**
     * Test transforming an EvRouting entity with comma decimal separator
     */
    public function testTransformWithCommaDecimalSeparator(): void
    {
        // Create mock EvRouting entity with comma as decimal separator
        $evRouting = $this->createMock(EvRouting::class);
        $evRouting->method('isEnabled')->willReturn(true);
        $evRouting->method('getLabel')->willReturn('eP21 (Peugeot 208)');
        $evRouting->method('getBrand')->willReturn('AP');
        $evRouting->method('getDvq')->willReturn('65');
        $evRouting->method('getB0f')->willReturn('ES');
        $evRouting->method('getDar')->willReturn('10');
        $evRouting->method('getMaxChargeInkWh')->willReturn('46,3');
        $evRouting->method('getVehicleHeight')->willReturn('1.430');

        // Transform the entity
        /** @var EvRouting&\PHPUnit\Framework\MockObject\MockObject $evRouting */
        $result = $this->transformer->transform($evRouting);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertEquals('46,3', $result['maxChargeInkWh']);
        $this->assertEquals('1.430', $result['vehicleHeight']);
    }
}
