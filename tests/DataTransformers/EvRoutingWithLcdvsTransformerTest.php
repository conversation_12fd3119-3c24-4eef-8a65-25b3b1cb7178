<?php

namespace App\Tests\DataTransformers;

use App\DataTransformers\EvRoutingWithLcdvsTransformer;
use App\Entity\EvRouting;
use App\Entity\LcdvEvRouting;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;

class EvRoutingWithLcdvsTransformerTest extends TestCase
{
    private EvRoutingWithLcdvsTransformer $transformer;

    protected function setUp(): void
    {
        $this->transformer = EvRoutingWithLcdvsTransformer::make();
    }

    /**
     * Test transforming an EvRouting entity with LCDVs to an array
     */
    public function testTransform(): void
    {
        // Create mock LCDV EV routing entities
        $lcdvEvRouting1 = $this->createMock(LcdvEvRouting::class);
        $lcdvEvRouting1->method('getLcdv')->willReturn('ABC123');

        $lcdvEvRouting2 = $this->createMock(LcdvEvRouting::class);
        $lcdvEvRouting2->method('getLcdv')->willReturn('DEF456');

        // Create mock EvRouting entity
        $evRouting = $this->createMock(EvRouting::class);
        $evRouting->method('isEnabled')->willReturn(true);
        $evRouting->method('getLabel')->willReturn('Test EV Routing');
        $evRouting->method('getConstantSpeedConsumptionInkWhPerHundredkm')->willReturn('20.5');
        $evRouting->method('getEngineType')->willReturn('electric');
        $evRouting->method('getMaxChargeInkWh')->willReturn('75');
        $evRouting->method('getVehicleMaxSpeed')->willReturn('180');
        $evRouting->method('getVehicleWeight')->willReturn('2000');
        $evRouting->method('getVehicleAxleWeight')->willReturn('1000');
        $evRouting->method('getVehicleLength')->willReturn('4.5');
        $evRouting->method('getVehicleWidth')->willReturn('1.8');
        $evRouting->method('getVehicleHeight')->willReturn('1.6');
        $evRouting->method('getAccelerationEfficiency')->willReturn('0.8');
        $evRouting->method('getDecelerationEfficiency')->willReturn('0.7');
        $evRouting->method('getUphillEfficiency')->willReturn('0.75');
        $evRouting->method('getDownhillEfficiency')->willReturn('0.85');
        $evRouting->method('getChargingCurveArray')->willReturn('[10,20,30,40,50]');
        $evRouting->method('getLcdvs')->willReturn(new ArrayCollection([$lcdvEvRouting1, $lcdvEvRouting2]));

        // Transform the entity
        $result = $this->transformer->transform($evRouting);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertTrue($result['Enabled']);
        $this->assertEquals('Test EV Routing', $result['Label']);
        $this->assertEquals(['ABC123', 'DEF456'], $result['LCDVs']);
        $this->assertEquals('20.5', $result['constantSpeedConsumptionInkWhPerHundredkm']);
        $this->assertEquals('electric', $result['engineType']);
        $this->assertEquals('75', $result['maxChargeInkWh']);
        $this->assertEquals('180', $result['vehicleMaxSpeed']);
        $this->assertEquals('2000', $result['vehicleWeight']);
        $this->assertEquals('1000', $result['vehicleAxleWeight']);
        $this->assertEquals('4.5', $result['vehicleLength']);
        $this->assertEquals('1.8', $result['vehicleWidth']);
        $this->assertEquals('1.6', $result['vehicleHeight']);
        $this->assertEquals('0.8', $result['accelerationEfficiency']);
        $this->assertEquals('0.7', $result['decelerationEfficiency']);
        $this->assertEquals('0.75', $result['uphillEfficiency']);
        $this->assertEquals('0.85', $result['downhillEfficiency']);
        $this->assertEquals('[10,20,30,40,50]', $result['chargingCurveArray']);
    }

    /**
     * Test transforming a collection of EvRouting entities
     */
    public function testCollection(): void
    {
        // Create mock EvRouting entities
        $evRouting1 = $this->createMock(EvRouting::class);
        $evRouting1->method('isEnabled')->willReturn(true);
        $evRouting1->method('getLabel')->willReturn('EV Routing 1');
        $evRouting1->method('getLcdvs')->willReturn(new ArrayCollection());

        $evRouting2 = $this->createMock(EvRouting::class);
        $evRouting2->method('isEnabled')->willReturn(true);
        $evRouting2->method('getLabel')->willReturn('EV Routing 2');
        $evRouting2->method('getLcdvs')->willReturn(new ArrayCollection());

        // Transform the collection
        $result = $this->transformer->collection([$evRouting1, $evRouting2]);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertEquals('EV Routing 1', $result[0]['Label']);
        $this->assertEquals('EV Routing 2', $result[1]['Label']);
    }

    /**
     * Test extracting LCDVs from a collection
     */
    public function testExtractLcdvs(): void
    {
        // Create mock LCDV EV routing entities
        $lcdvEvRouting1 = $this->createMock(LcdvEvRouting::class);
        $lcdvEvRouting1->method('getLcdv')->willReturn('ABC123');

        $lcdvEvRouting2 = $this->createMock(LcdvEvRouting::class);
        $lcdvEvRouting2->method('getLcdv')->willReturn('DEF456');

        // Create a collection
        $collection = new ArrayCollection([$lcdvEvRouting1, $lcdvEvRouting2]);

        // Use reflection to access protected method
        $reflectionClass = new \ReflectionClass(EvRoutingWithLcdvsTransformer::class);
        $method = $reflectionClass->getMethod('extractLcdvs');
        $method->setAccessible(true);

        // Call the method
        $result = $method->invokeArgs(null, [$collection]);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertEquals('ABC123', $result[0]);
        $this->assertEquals('DEF456', $result[1]);
    }

    /**
     * Test transforming an EvRouting entity with empty LCDVs collection
     */
    public function testTransformWithEmptyLcdvs(): void
    {
        // Create mock EvRouting entity with empty LCDVs collection
        $evRouting = $this->createMock(EvRouting::class);
        $evRouting->method('isEnabled')->willReturn(true);
        $evRouting->method('getLabel')->willReturn('Test EV Routing');
        $evRouting->method('getLcdvs')->willReturn(new ArrayCollection());

        // Transform the entity
        $result = $this->transformer->transform($evRouting);

        // Assert the result
        $this->assertIsArray($result);
        $this->assertTrue($result['Enabled']);
        $this->assertEquals('Test EV Routing', $result['Label']);
        $this->assertEmpty($result['LCDVs']);
    }
}
