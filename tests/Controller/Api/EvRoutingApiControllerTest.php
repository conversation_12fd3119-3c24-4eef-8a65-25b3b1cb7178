<?php

namespace App\Tests\Controller\Api;

use App\Controller\Api\EvRoutingApiController;
use App\DataTransformers\EvRoutingTransformer;
use App\DataTransformers\EvRoutingWithLcdvsTransformer;
use App\Entity\EvRouting;
use App\Entity\LcdvEvRouting;
use App\Repository\LcdvEvRoutingRepository;
use App\Service\EvRoutingService;
use App\Service\VehicleLabelService;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;
use Psr\Container\ContainerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class EvRoutingApiControllerTest extends TestCase
{
    private EvRoutingApiController $controller;
    private ValidatorInterface $validator;
    private LcdvEvRoutingRepository $lcdvEvRoutingRepository;
    private VehicleLabelService $vehicleLabelService;
    private EvRoutingService $evRoutingService;

    protected function setUp(): void
    {
        $this->controller = new EvRoutingApiController();

        // Set up container with proper serializer
        $container = $this->createMock(ContainerInterface::class);

        // Mock the has method to return true for any service
        $container->method('has')->willReturn(true);

        // Create a serializer that actually works
        $encoder = new \Symfony\Component\Serializer\Encoder\JsonEncoder();
        $normalizer = new \Symfony\Component\Serializer\Normalizer\ObjectNormalizer();
        $serializer = new \Symfony\Component\Serializer\Serializer([$normalizer], [$encoder]);

        // Create a map for different services
        $containerMap = [
            ['twig', $this->createMock(\Twig\Environment::class)],
            ['serializer', $serializer],
            ['parameter_bag', $this->createMock(\Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface::class)]
        ];

        // Configure the get method to return appropriate mocks based on the service name
        $container->method('get')
            ->willReturnCallback(function ($service) use ($containerMap) {
                foreach ($containerMap as [$name, $mock]) {
                    if ($service === $name) {
                        return $mock;
                    }
                }
                return null;
            });

        $this->controller->setContainer($container);

        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->lcdvEvRoutingRepository = $this->createMock(LcdvEvRoutingRepository::class);
        $this->vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $this->evRoutingService = $this->createMock(EvRoutingService::class);
    }

    /**
     * Test successful retrieval of EV routing configuration
     */
    public function testIndexSuccess(): void
    {
        // Create request with valid parameters
        $request = Request::create(
            '/v1/ev_routing',
            'GET',
            [
                'lcdv' => 'ABC123',
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        // Mock validator to return no errors
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create mock EV routing entity
        $evRouting = $this->createMock(EvRouting::class);
        $evRouting->method('isEnabled')->willReturn(true);
        $evRouting->method('getLabel')->willReturn('Test EV Routing');
        $evRouting->method('getConstantSpeedConsumptionInkWhPerHundredkm')->willReturn('20.5');
        $evRouting->method('getEngineType')->willReturn('electric');
        $evRouting->method('getMaxChargeInkWh')->willReturn('75');
        $evRouting->method('getVehicleMaxSpeed')->willReturn('180');
        $evRouting->method('getVehicleWeight')->willReturn('2000');
        $evRouting->method('getVehicleAxleWeight')->willReturn('1000');
        $evRouting->method('getVehicleLength')->willReturn('4.5');
        $evRouting->method('getVehicleWidth')->willReturn('1.8');
        $evRouting->method('getVehicleHeight')->willReturn('1.6');
        $evRouting->method('getAccelerationEfficiency')->willReturn('0.8');
        $evRouting->method('getDecelerationEfficiency')->willReturn('0.7');
        $evRouting->method('getUphillEfficiency')->willReturn('0.75');
        $evRouting->method('getDownhillEfficiency')->willReturn('0.85');
        $evRouting->method('getChargingCurveArray')->willReturn('[10,20,30,40,50]');

        // Create mock LCDV EV routing entity
        $lcdvEvRouting = $this->createMock(LcdvEvRouting::class);
        $lcdvEvRouting->method('getEvRouting')->willReturn($evRouting);

        // Mock repository to return the LCDV EV routing
        $this->lcdvEvRoutingRepository->expects($this->once())
            ->method('getEvRoutingConfig')
            ->with('ABC123', 'DVQ123', 'B0F123', 'DAR123')
            ->willReturn(new ArrayCollection([$lcdvEvRouting]));

        // Call the controller method
        $response = $this->controller->index(
            $request,
            $this->validator,
            $this->lcdvEvRoutingRepository,
            $this->vehicleLabelService
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Check response content
        $content = json_decode($response->getContent(), true);
        $this->assertTrue($content['success']);
        $this->assertEquals(200, $content['status']);
        $this->assertEquals('EV Routing configuration retrieved successfully', $content['message']);
        $this->assertTrue($content['data']['enabled']);
        $this->assertEquals('Test EV Routing', $content['data']['label']);
        $this->assertEquals('20.5', $content['data']['constantSpeedConsumptionInkWhPerHundredkm']);
        $this->assertEquals('electric', $content['data']['engineType']);
        $this->assertEquals('75', $content['data']['maxChargeInkWh']);
    }

    /**
     * Test validation failure in EV routing configuration retrieval
     */
    public function testIndexValidationFailure(): void
    {
        // Create request with invalid parameters
        $request = Request::create(
            '/v1/ev_routing',
            'GET',
            [
                'lcdv' => '', // Empty LCDV should fail validation
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        // Create mock validation error
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('[lcdv]');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violationList = new ConstraintViolationList([$violation]);

        // Mock validator to return errors
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the controller method
        $response = $this->controller->index(
            $request,
            $this->validator,
            $this->lcdvEvRoutingRepository,
            $this->vehicleLabelService
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        // Check response content
        $content = json_decode($response->getContent(), true);
        // Just check that we have the validation error for lcdv
        $this->assertArrayHasKey('errors', $content);
        $this->assertEquals('This value should not be blank.', $content['errors']['lcdv']);
    }

    /**
     * Test case when EV routing configuration does not exist
     */
    public function testIndexConfigNotFound(): void
    {
        // Create request with valid parameters
        $request = Request::create(
            '/v1/ev_routing',
            'GET',
            [
                'lcdv' => 'NONEXISTENT',
                'dvq' => 'DVQ123',
                'b0f' => 'B0F123',
                'dar' => 'DAR123',
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        // Mock validator to return no errors
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock repository to return empty collection (no config found)
        $this->lcdvEvRoutingRepository->expects($this->once())
            ->method('getEvRoutingConfig')
            ->willReturn(new ArrayCollection());

        // Call the controller method
        $response = $this->controller->index(
            $request,
            $this->validator,
            $this->lcdvEvRoutingRepository,
            $this->vehicleLabelService
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        // Check response content
        $content = json_decode($response->getContent(), true);
        // Just check for the message
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('The Ev Routing config does not exist!', $content['message']);
    }

    /**
     * Test successful retrieval of all EV routings for a brand
     */
    public function testGetAllVehiclesByBrandSuccess(): void
    {
        // Create request with valid parameters
        $request = Request::create(
            '/v1/ev_routings',
            'GET',
            [
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        // Mock validator to return no errors
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create mock EV routing entities
        $evRouting1 = $this->createMock(EvRouting::class);
        $evRouting1->method('isEnabled')->willReturn(true);
        $evRouting1->method('getLabel')->willReturn('EV Routing 1');
        $evRouting1->method('getLcdvs')->willReturn(new ArrayCollection());

        $evRouting2 = $this->createMock(EvRouting::class);
        $evRouting2->method('isEnabled')->willReturn(true);
        $evRouting2->method('getLabel')->willReturn('EV Routing 2');
        $evRouting2->method('getLcdvs')->willReturn(new ArrayCollection());

        // Mock service to return EV routings
        $this->evRoutingService->expects($this->once())
            ->method('normalizeApiData')
            ->with('DS')
            ->willReturn([$evRouting1, $evRouting2]);

        // Call the controller method
        $response = $this->controller->getAllVehiclesByBrand(
            $request,
            $this->evRoutingService,
            $this->validator
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Check response content
        $content = json_decode($response->getContent(), true);
        $this->assertTrue($content['success']);
        $this->assertEquals(200, $content['status']);
        $this->assertEquals('EV Routing configurations retrieved successfully', $content['message']);
        $this->assertIsArray($content['data']);
        $this->assertCount(2, $content['data']);
        $this->assertEquals('EV Routing 1', $content['data'][0]['Label']);
        $this->assertEquals('EV Routing 2', $content['data'][1]['Label']);
    }

    /**
     * Test case when no EV routings are found for a brand
     */
    public function testGetAllVehiclesByBrandNoResults(): void
    {
        // Create request with valid parameters
        $request = Request::create(
            '/v1/ev_routings',
            'GET',
            [
                'brand' => 'DS',
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        // Mock validator to return no errors
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock service to return empty array (no EV routings found)
        $this->evRoutingService->expects($this->once())
            ->method('normalizeApiData')
            ->with('DS')
            ->willReturn([]);

        // Call the controller method
        $response = $this->controller->getAllVehiclesByBrand(
            $request,
            $this->evRoutingService,
            $this->validator
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());

        // Check response content
        $content = json_decode($response->getContent(), true);
        // Just check for the message
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('No EV routings found for this brand', $content['message']);
    }

    /**
     * Test validation failure in retrieving all EV routings
     */
    public function testGetAllVehiclesByBrandValidationFailure(): void
    {
        // Create request with invalid parameters
        $request = Request::create(
            '/v1/ev_routings',
            'GET',
            [
                'brand' => 'INVALID_BRAND', // Invalid brand should fail validation
                'country' => 'FR',
                'language' => 'fr'
            ]
        );

        // Create mock validation error
        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('[brand]');
        $violation->method('getMessage')->willReturn('This value is not valid.');

        $violationList = new ConstraintViolationList([$violation]);

        // Mock validator to return errors
        $this->validator = $this->getMockBuilder(ValidatorInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violationList);

        // Call the controller method
        $response = $this->controller->getAllVehiclesByBrand(
            $request,
            $this->evRoutingService,
            $this->validator
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());

        // Check response content
        $content = json_decode($response->getContent(), true);
        // Just check for the errors
        $this->assertArrayHasKey('errors', $content);
        $this->assertEquals('This value is not valid.', $content['errors']['brand']);
    }
}
