<?php

namespace App\Tests\Service;

use App\Entity\EvRouting;
use App\Repository\EvRoutingRepository;
use App\Service\EvRoutingService;
use PHPUnit\Framework\TestCase;

class EvRoutingServiceTest extends TestCase
{
    private EvRoutingService $evRoutingService;
    private EvRoutingRepository $evRoutingRepository;

    protected function setUp(): void
    {
        $this->evRoutingRepository = $this->createMock(EvRoutingRepository::class);
        /** @var EvRoutingRepository|\PHPUnit\Framework\MockObject\MockObject */
        $this->evRoutingService = new EvRoutingService($this->evRoutingRepository);
    }

    /**
     * Test normalizing API data for a specific brand
     */
    public function testNormalizeApiData(): void
    {
        // Create mock EV routing entities
        $evRouting1 = $this->createMock(EvRouting::class);
        $evRouting1->method('getLabel')->willReturn('EV Routing 1');
        $evRouting1->method('getBrand')->willReturn('DS');

        $evRouting2 = $this->createMock(EvRouting::class);
        $evRouting2->method('getLabel')->willReturn('EV Routing 2');
        $evRouting2->method('getBrand')->willReturn('DS');

        // Mock repository to return EV routings
        $this->evRoutingRepository->expects($this->once())
            ->method('findBy')
            ->with(['brand' => 'DS'])
            ->willReturn([$evRouting1, $evRouting2]);

        // Call the service method
        $result = $this->evRoutingService->normalizeApiData('DS');

        // Assert result
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertSame($evRouting1, $result[0]);
        $this->assertSame($evRouting2, $result[1]);
    }

    /**
     * Test normalizing API data with no results
     */
    public function testNormalizeApiDataNoResults(): void
    {
        // Mock repository to return empty array
        $this->evRoutingRepository->expects($this->once())
            ->method('findBy')
            ->with(['brand' => 'NONEXISTENT'])
            ->willReturn([]);

        // Call the service method
        $result = $this->evRoutingService->normalizeApiData('NONEXISTENT');

        // Assert result
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Test normalizing API data with DS brand
     */
    public function testNormalizeApiDataDsBrand(): void
    {
        // Create mock EV routing entity for DS brand
        $dsEvRouting = $this->createMock(EvRouting::class);
        $dsEvRouting->method('getLabel')->willReturn('DS EV Routing');
        $dsEvRouting->method('getBrand')->willReturn('DS');

        // Test DS brand
        $this->evRoutingRepository->expects($this->once())
            ->method('findBy')
            ->with(['brand' => 'DS'])
            ->willReturn([$dsEvRouting]);

        $result = $this->evRoutingService->normalizeApiData('DS');
        $this->assertCount(1, $result);
        $this->assertSame($dsEvRouting, $result[0]);
    }

    /**
     * Test normalizing API data with Peugeot brand
     */
    public function testNormalizeApiDataPeugeotBrand(): void
    {
        // Create mock EV routing entity for Peugeot brand
        $peugeotEvRouting = $this->createMock(EvRouting::class);
        $peugeotEvRouting->method('getLabel')->willReturn('Peugeot EV Routing');
        $peugeotEvRouting->method('getBrand')->willReturn('PEUGEOT');

        // Test Peugeot brand
        $this->evRoutingRepository->expects($this->once())
            ->method('findBy')
            ->with(['brand' => 'PEUGEOT'])
            ->willReturn([$peugeotEvRouting]);

        $result = $this->evRoutingService->normalizeApiData('PEUGEOT');
        $this->assertCount(1, $result);
        $this->assertSame($peugeotEvRouting, $result[0]);
    }
}
