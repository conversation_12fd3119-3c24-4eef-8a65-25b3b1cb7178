<?php

namespace App\Tests\Repository;

use App\Entity\EvRouting;
use App\Entity\LcdvEvRouting;
use App\Repository\LcdvEvRoutingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class LcdvEvRoutingRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private LcdvEvRoutingRepository $repository;

    protected function setUp(): void
    {
        self::bootKernel();
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->repository = $this->entityManager->getRepository(LcdvEvRouting::class);
        
        // Set up test data
        $this->setupTestData();
    }

    /**
     * Set up test data for repository tests
     */
    private function setupTestData(): void
    {
        // Create test EV routing
        $evRouting = new EvRouting();
        $evRouting->setEnabled(true);
        $evRouting->setLabel('Test EV Routing');
        $evRouting->setBrand('DS');
        $evRouting->setConstantSpeedConsumptionInkWhPerHundredkm('20.5');
        $evRouting->setEngineType('electric');
        $evRouting->setMaxChargeInkWh('75');
        $evRouting->setVehicleMaxSpeed('180');
        $evRouting->setVehicleWeight('2000');
        $evRouting->setVehicleAxleWeight('1000');
        $evRouting->setVehicleLength('4.5');
        $evRouting->setVehicleWidth('1.8');
        $evRouting->setVehicleHeight('1.6');
        $evRouting->setAccelerationEfficiency('0.8');
        $evRouting->setDecelerationEfficiency('0.7');
        $evRouting->setUphillEfficiency('0.75');
        $evRouting->setDownhillEfficiency('0.85');
        $evRouting->setChargingCurveArray('[10,20,30,40,50]');
        $evRouting->setDvq('DVQ123');
        $evRouting->setB0f('B0F123');
        $evRouting->setDar('DAR123');
        
        $this->entityManager->persist($evRouting);
        
        // Create LCDV EV routing associations with different LCDV codes
        $lcdvCodes = ['AB', 'ABC', 'ABCD', 'ABCDE', 'ABCDEF'];
        
        foreach ($lcdvCodes as $lcdv) {
            $lcdvEvRouting = new LcdvEvRouting();
            $lcdvEvRouting->setLcdv($lcdv);
            $lcdvEvRouting->setEvRouting($evRouting);
            
            $this->entityManager->persist($lcdvEvRouting);
        }
        
        $this->entityManager->flush();
    }

    /**
     * Test getting LCDV EV routings where LCDV matches
     */
    public function testGetLcdvEvRoutingsWhereInLcdv(): void
    {
        $result = $this->repository->getLcdvEvRoutingsWhereInLcdv('ABCDEF');
        
        $this->assertCount(5, $result); // Should match AB, ABC, ABCD, ABCDE, ABCDEF
        
        // Check that all results have the correct LCDV prefixes
        $lcdvs = [];
        foreach ($result as $lcdvEvRouting) {
            $lcdvs[] = $lcdvEvRouting->getLcdv();
        }
        
        $this->assertContains('AB', $lcdvs);
        $this->assertContains('ABC', $lcdvs);
        $this->assertContains('ABCD', $lcdvs);
        $this->assertContains('ABCDE', $lcdvs);
        $this->assertContains('ABCDEF', $lcdvs);
    }

    /**
     * Test getting EV routing configuration with specific parameters
     */
    public function testGetEvRoutingConfig(): void
    {
        $result = $this->repository->getEvRoutingConfig('ABCDEF', 'DVQ123', 'B0F123', 'DAR123');
        
        $this->assertCount(5, $result); // Should match all 5 LCDV codes
        
        // Check the first result
        $firstResult = $result->first();
        $this->assertInstanceOf(LcdvEvRouting::class, $firstResult);
        
        // Check the associated EV routing
        $evRouting = $firstResult->getEvRouting();
        $this->assertInstanceOf(EvRouting::class, $evRouting);
        $this->assertEquals('Test EV Routing', $evRouting->getLabel());
        $this->assertEquals('DVQ123', $evRouting->getDvq());
        $this->assertEquals('B0F123', $evRouting->getB0f());
        $this->assertEquals('DAR123', $evRouting->getDar());
    }

    /**
     * Test getting EV routing configuration with non-matching parameters
     */
    public function testGetEvRoutingConfigNoMatch(): void
    {
        // Test with non-matching DVQ
        $result = $this->repository->getEvRoutingConfig('ABCDEF', 'NONEXISTENT', 'B0F123', 'DAR123');
        $this->assertCount(0, $result);
        
        // Test with non-matching B0F
        $result = $this->repository->getEvRoutingConfig('ABCDEF', 'DVQ123', 'NONEXISTENT', 'DAR123');
        $this->assertCount(0, $result);
        
        // Test with non-matching DAR
        $result = $this->repository->getEvRoutingConfig('ABCDEF', 'DVQ123', 'B0F123', 'NONEXISTENT');
        $this->assertCount(0, $result);
        
        // Test with non-matching LCDV
        $result = $this->repository->getEvRoutingConfig('NONEXISTENT', 'DVQ123', 'B0F123', 'DAR123');
        $this->assertCount(0, $result);
    }

    /**
     * Test getting LCDVs with a specific prefix
     */
    public function testGetLCDVs(): void
    {
        $result = $this->repository->getLCDVs('ABCDEF');
        
        $this->assertGreaterThanOrEqual(1, count($result));
        
        // Check that all results have the correct LCDV prefix
        foreach ($result as $lcdvEvRouting) {
            $this->assertStringStartsWith('AB', $lcdvEvRouting->getLcdv());
        }
    }

    /**
     * Test finding unique LCDV EV routing
     */
    public function testFindUniqueLcdvEvRouting(): void
    {
        // Get an existing EV routing
        $evRouting = $this->entityManager->getRepository(EvRouting::class)->findOneBy(['label' => 'Test EV Routing']);
        
        // Test with existing LCDV
        $fields = [
            'lcdv' => 'ABC',
            'evRouting' => $evRouting
        ];
        
        $result = $this->repository->findUniqueLcdvEvRouting($fields);
        
        $this->assertCount(1, $result);
        $this->assertEquals('ABC', $result[0]->getLcdv());
        $this->assertEquals($evRouting, $result[0]->getEvRouting());
        
        // Test with non-existent LCDV
        $fields = [
            'lcdv' => 'NONEXISTENT',
            'evRouting' => $evRouting
        ];
        
        $result = $this->repository->findUniqueLcdvEvRouting($fields);
        
        $this->assertCount(0, $result);
    }

    protected function tearDown(): void
    {
        // Clean up test data
        $lcdvEvRoutings = $this->repository->findAll();
        foreach ($lcdvEvRoutings as $lcdvEvRouting) {
            $this->entityManager->remove($lcdvEvRouting);
        }
        
        $evRoutings = $this->entityManager->getRepository(EvRouting::class)->findAll();
        foreach ($evRoutings as $evRouting) {
            $this->entityManager->remove($evRouting);
        }
        
        $this->entityManager->flush();
        
        parent::tearDown();
    }
}
