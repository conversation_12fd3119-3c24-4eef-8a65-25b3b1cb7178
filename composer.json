{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1.0", "ext-ctype": "*", "ext-iconv": "*", "aws/aws-sdk-php": "^3.133", "doctrine/annotations": "1.*", "doctrine/dbal": "^3.6", "doctrine/doctrine-bundle": "^2.8", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.14", "drenso/symfony-oidc-bundle": "^2.9", "guzzlehttp/guzzle": "^7.9", "jms/serializer-bundle": "^5.2", "nelmio/api-doc-bundle": "^5.0", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.15", "psr/http-client": "^1.0", "sebastian/diff": "^4.0", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^1.17|^2", "symfony/form": "6.4.*", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/intl": "6.4.*", "symfony/mailer": "6.4.*", "symfony/messenger": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "6.4.*", "symfony/process": "6.4.*", "symfony/property-access": "6.4.*", "symfony/property-info": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/string": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/uid": "6.4.*", "symfony/validator": "6.4.*", "symfony/web-link": "6.4.*", "symfony/yaml": "6.4.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0", "webmozart/assert": "^1.11"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "*", "fakerphp/faker": "*", "friendsofphp/php-cs-fixer": "^3.17", "hautelook/alice-bundle": "*", "php-mock/php-mock-phpunit": "^2.10", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^9.5", "rector/rector": "^0.17.0", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/maker-bundle": "^1.48", "symfony/phpunit-bridge": "^7.0", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*", "theofidry/alice-data-fixtures": "*"}}